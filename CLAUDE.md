# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React Native Expo application called "Mon parfum idéal" (My Perfect Perfume) - a mobile app for perfume discovery and recommendations. The app uses file-based routing with Expo Router and includes features for browsing perfumes, search functionality, user authentication, and favorites management.

## Development Commands

### Essential Development Commands
- `yarn start` or `expo start` - Start the development server
- `yarn android` - Run on Android (with DARK_MODE=media env var)
- `yarn ios` - Run on iOS (with DARK_MODE=media env var)
- `yarn test` - Run tests with Jest in watch mode
- `yarn lint` - Run ESLint for code quality
- `yarn generate:types` - Generate TypeScript types from OpenAPI spec (requires localhost:3001/swagger endpoint)

### Project Management
- `yarn reset-project` - Reset project to blank state (moves starter code to app-example)
- `yarn postinstall` - Apply patches after dependency installation

## Architecture Overview

### Core Stack
- **Framework**: React Native with Expo SDK 53
- **Navigation**: Expo Router (file-based routing)
- **State Management**: Rematch/Redux with plugins for loading states and selectors
- **Styling**: NativeWind (TailwindCSS for React Native) + Gluestack UI components
- **API Layer**: Axios with SWR for data fetching
- **Internationalization**: i18n-js with context providers
- **Error Tracking**: Sentry integration

### Key Architecture Patterns

#### Provider Pattern
The app uses a nested provider structure in `providers/index.tsx`:
```
SwrProvider > ThemeProvider > ReduxProvider > I18nProvider > CurrencyProvider > TranslatedConstantsProvider
```

#### State Management
- **Rematch Models**: Located in `models/` directory (auth, favorites, search)
- **Store Configuration**: `store.ts` with loading and select plugins
- **Async Storage**: For persisting user preferences and favorites

#### Component Structure
- **UI Components**: Gluestack UI components in `components/ui/`
- **Domain Components**: Organized by feature (home, search, perfumes, profile, etc.)
- **Common Components**: Reusable components in `components/common/`

### File Structure Highlights

#### App Router Structure
- `app/` - File-based routing with Expo Router
- `app/(app)/(tabs)/` - Main tabbed navigation
- `app/(app)/(tabs)/(home,search,favorites)/` - Parallel routes for main tabs
- Authentication screens at root level (sign-in, register, etc.)

#### Key Directories
- `components/` - All React components organized by domain
- `constants/` - App constants, colors, currencies, languages
- `providers/` - Context providers for global state
- `hooks/` - Custom React hooks
- `utils/` - Utility functions and helpers
- `locales/` - Translation files (17+ languages supported)
- `api/` - API configuration and axios setup
- `assets/` - Images, icons, and static assets

## Development Guidelines

### TypeScript Configuration
- Uses strict mode with Expo TypeScript base config
- Path aliases: `@/*` maps to project root
- API types generated from OpenAPI spec in `@types/api.types.ts`

### Styling Approach
- **Primary**: NativeWind (TailwindCSS) with `global.css`
- **UI Library**: Gluestack UI components for consistent design
- **Configuration**: `tailwind.config.js` and `gluestack-ui.config.json`

### Fonts
- **Primary**: Playfair Display (serif, for headlines)
- **Secondary**: Overpass (sans-serif, for body text)
- Loaded via Expo Google Fonts

### Environment Configuration
- Uses Expo environment variables
- Sentry DSN and API URL configured via `EXPO_PUBLIC_*` vars
- Environment-specific builds controlled via `process.env.EXPO_PUBLIC_ENV`

### Testing
- **Framework**: Jest with Expo preset
- **Configuration**: Basic Jest setup in package.json
- Run tests with `yarn test`

### Code Quality
- **Linting**: ESLint with Expo config, Prettier integration
- **Patches**: Uses patch-package for dependency modifications
- **Type Safety**: Strict TypeScript enforcement

## Platform-Specific Notes

### Android
- Target SDK: 35, Compile SDK: 35
- Build tools: 35.0.0
- Uses adaptive icons with black background

### iOS
- No tablet support (supportsTablet: false)
- Bundle ID: fr.lunify.mpi
- Localization support for FR/EN

### Build Configuration
- **EAS Build**: Configured with project ID and update URL
- **Sentry**: Integrated for crash reporting and performance monitoring
- **Updates**: Uses Expo Updates with app version policy

## Important Files to Understand

- `app/_layout.tsx` - Root layout with providers and font loading
- `store.ts` - Redux store configuration
- `providers/index.tsx` - Provider composition and user state loading
- `api/axios.ts` - API client configuration with interceptors
- `constants/constants.ts` - App-wide constants and configuration
- `utils/i18n.ts` - Internationalization utilities

## Development Tips

- The app supports 17+ languages with organized locale files
- Uses SWR for efficient data fetching and caching
- Implements both light and dark theme support
- Currency and language can be changed dynamically
- Favorites are persisted locally and sync with backend
- Uses Expo Router for type-safe navigation
- Implements proper error boundaries and loading states via Rematch plugins