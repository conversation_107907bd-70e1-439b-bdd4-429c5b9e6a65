import React, { createContext, useContext, useEffect, useState } from "react";
import {
  createTranslatedConstants,
  getFamilyLabel as getFamilyLabelFunc,
  FAMILIES as FAMILIES_CONST,
} from "@/constants/constants";
import { i18nEvents, LANGUAGE_CHANGE_EVENT } from "@/utils/i18n";

// Tạo type cho context
type TranslatedConstantsContextType = ReturnType<typeof createTranslatedConstants> & {
  getGenderLabel: (key: string) => string;
  getFamilyLabel: (key: string) => string;
  FAMILIES: typeof FAMILIES_CONST;
};

// Tạo context
const TranslatedConstantsContext = createContext<TranslatedConstantsContextType | null>(null);

// Provider component
export const TranslatedConstantsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [constants, setConstants] = useState(createTranslatedConstants());

  useEffect(() => {
    // Update constants when language changes
    const handleLanguageChange = () => {
      setConstants(createTranslatedConstants());
    };

    // Listen for language change events
    i18nEvents.on(LANGUAGE_CHANGE_EVENT, handleLanguageChange);

    // Clean up event listener
    return () => {
      i18nEvents.off(LANGUAGE_CHANGE_EVENT, handleLanguageChange);
    };
  }, []);

  // Tạo phiên bản động của hàm getGenderLabel sử dụng giá trị constants hiện tại
  const getGenderLabel = (key: string): string => {
    return constants.GENDER_LABELS[key] || "Inconnu";
  };

  // Tạo phiên bản động của hàm getFamilyLabel sử dụng giá trị constants hiện tại
  const getFamilyLabel = (key: string): string => {
    return constants.FAMILY_LABELS[key] || "Inconnu";
  };

  const value = {
    ...constants,
    getGenderLabel,
    getFamilyLabel,
    FAMILIES: FAMILIES_CONST,
  };

  return (
    <TranslatedConstantsContext.Provider value={value}>
      {children}
    </TranslatedConstantsContext.Provider>
  );
};

// Custom hook để sử dụng context
export const useTranslatedConstants = () => {
  const context = useContext(TranslatedConstantsContext);
  if (!context) {
    throw new Error("useTranslatedConstants must be used within a TranslatedConstantsProvider");
  }
  return context;
};
