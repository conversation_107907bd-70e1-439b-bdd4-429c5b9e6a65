import * as React from "react";
import Svg, { Defs, LinearGradient, Stop, Path } from "react-native-svg";

function LogoSvg(props: any) {
  return (
    <Svg
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      width={props.width}
      height={props.height}
      viewBox="0 0 272 149.3"
      {...props}
    >
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1={135.9}
          y1={134.7}
          x2={135.9}
          y2={2.6}
          gradientUnits="userSpaceOnUse"
        >
          <Stop offset={0} stopColor="#dcb423" />
          <Stop offset={0.1} stopColor="#ecce53" />
          <Stop offset={0.3} stopColor="#e2c04f" />
          <Stop offset={0.4} stopColor="#f7e159" />
          <Stop offset={0.5} stopColor="#cea728" />
          <Stop offset={0.7} stopColor="#edcf4e" />
          <Stop offset={1} stopColor="#dbb429" />
        </LinearGradient>
      </Defs>
      <Path
        d="M147.9 47.6v-7.3c-.6 0-1-.1-1.4-.1h-12.8c-2 0-4.1.5-6.1.8-1.7.3-3.5.6-5.2.8l-6 .9c-.3 0-.7 0-1.4-.2 1.3-1 2.2-1.8 3.2-2.6 1.7-1.3 3.6-2.6 5.3-4 .7-.6 1.4-.9 2.3-.8 1.3.1 2.5.2 3.8.3h51.3c2.2 0 4.4.5 6.6.6 3.4.2 6.6 1.2 9.8 2.3 2.8 1 5.4 2.3 7.7 4.2 1.8 1.4 3.3 3.1 4.8 4.9-.7.4-1.3.7-2 .7h-13.7c-1.3-2.6-3.4-4.4-5.5-6.2-1.1-.9-2.3-1.5-3.7-1.9-3.5-1-7.2-1.1-10.8-1.3-3.7-.2-7.5 0-11.3 0-.4 0-.8 0-1.4.2 0 3 0 5.8-.2 8.6h-13.3zm-95.3 74.1c-3.6-9.2-7.3-18.4-10.8-27.8 2.9-.1 5.7-.2 8.5-.2h1.9c1 2.5 2.1 5 3 7.5 2 5 4 10.1 5.9 15.1.1.4.4.7.7 1.2.7-1.5 1.4-2.8 2-4.1 1-2.2 1.9-4.4 2.8-6.6 1.5-3.5 2.9-6.9 4.4-10.4.4-.9.8-1.7 1.5-2.7 1.6-.1 2.9-.2 4.3 0 .6 0 1.1.3 1.7.5-1 2.5-1.9 4.9-2.9 7.4-1.3 3.2-2.5 6.4-3.8 9.5-1.1 2.8-2.3 5.6-3.4 8.5-.2.5-.4 1-.9 1.6-4.3.1-8.3 0-12.3.1-.9 0-1.8.2-2.6.3zm95.6-28.6c0-3.8 0-7.6.1-11.6 4.4-.2 8.7-.2 13 0 0 .7.1 1.2.1 1.8h2.8c2.8-.2 5.6-.4 8.4-.7 1.6-.1 3.2-.2 4.8-.4 1-.1 2.1-.3 3.1-.6 1.6-.4 3.3-.9 5-1.3.8.3 1.6.6 2.3.6h14.9c.6 0 1.3.3 1.9.4-2.2 2.7-5.1 4.4-8.3 5.7-3.4 1.4-7 2.3-10.7 2.7-2.7.3-5.5.7-8.2 1-2.2.2-4.3.2-6.5.4-1.2 0-2.4.3-3.6.3-1.8 0-3.6-.3-5.6-.4 0 .8 0 1.3-.3 2-4.6.1-8.9.1-13.3.1zm0 .1c4.4-.2 8.8-.2 13.3-.2.2.3.2.7.2 1 0 9.1 0 18.2-.2 27.5-4.6.2-8.9.2-13.3.2v-3.8-24.6zm107.7-.1v1c0 9.1 0 18.2-.2 27.5-4.6.2-8.9.2-13.3.2V118c0-8.2 0-16.3.2-24.6 4.6-.2 8.9-.2 13.3-.2zM96.8 120.7v-4.2c0-7.1 0-14.3.2-21.6 4.6-.2 9-.2 13.4-.2v1c0 8.2 0 16.4-.2 24.8-4.6.2-9 .2-13.4.2zm145.7-71.4v-9h-14.4c-1.9 0-3.7.4-5.6.7-2 .3-3.9.7-5.9 1-2.1.3-4.2.7-6.4 1-.2 0-.4 0-1-.2.9-.8 1.6-1.3 2.2-1.8 2-1.6 4.1-3.2 6.1-4.8.8-.6 1.7-.9 2.8-.9 3.4.2 6.8.3 10.2.3h25.4c0 4.6 0 9-.2 13.6-4.6.1-8.9.1-13.3.1zm-.1 72.5c4.4-.2 8.8-.2 13.3-.2.3 1.2.3 2.3.5 3.5.6 3.6 2.9 5.6 6.4 6.3 2 .4 4 .8 6.1.7h1.9v2.5h-28.2c-.2-4.2 0-8.4 0-12.8zm-28-62.8c.7 1.5.5 3.2.4 4.7-.3 2.7-.9 5.4-2.3 8h-17.8c1.4-3.6 2.2-7.1 2.1-10.8 0-.6 0-1.2.2-1.8h17.4zm-66.2 62.8c4.4-.2 8.8-.2 13.3-.2.2 4.3.2 8.6.2 13h-28.2v-2.2h1.9c2.6-.3 5.2-.3 7.7-1.3 3.1-1.2 5.1-3.8 5.1-7v-2.3zM92.6 48c.1-.4.3-.8.5-1.4.1-.2.2-.3.2-.4 1.5-3.4 3-6.8 4.4-10.3.3-.6.7-.8 1.3-.8h7.2c0-.7 0-1.2.2-1.9 1.5-.1 2.7-.1 4-.2.7 4.9.3 9.8.4 14.6 0 .4-.2.8-.5 1.4-1.1 0-1.9-.2-2.8-.2H100c-.9 0-1.7.2-2.7.3h-.7c-1.5-.2-2.9-.4-4.2-.7 0 0 0-.1.1-.3.1-.2.2-.2.2-.3zM23.5 95.4v1c0 9 0 18-.2 27.1-2.6.2-5 .2-7.4.2 0-4-.3-8 0-11.9.5-5.4 0-10.7.4-16.2 1.1-.3 2-.5 2.9-.4 1.4 0 2.7.2 4.1.3zm86.9-.8c-4.4.2-8.9.2-13.4.2-.2-4.9-.2-9.7 0-14.7 4.6-.1 9-.1 13.4 0v14.6zM31.2 40.8c.3.7.5 1.5.8 2.4 0 .2.1.3.2.3 0 0 0 .1.1.4-.4 1.1-1.2.9-1.9.9H16.7c-.7-3.3-2.5-5.5-6-6.2-1.2-.3-2.4-.6-3.9-.7-.2-.1-.3-.2-.4-.2H2.2v-2.6H27q1.9 0 2.6 1.9c0 .3.1.3.2.3.1.4.3.8.5 1.3 0 .2.1.3.2.3.1.4.3.8.5 1.3 0 .2.1.3.2.3zm117 31.7V66c0-2.4-.2-4.9-.1-7.5 4.6-.2 8.9-.2 13.3 0 0 4.8 0 9.4-.2 14h-12.9zm66.2-13.6c-5.8.1-11.6.1-17.5 0-.4-3.8-1.2-7.3-2.8-10.6h13.6c.6 0 1.2-.3 2-.5.9.4 1.2 1.2 1.6 2.1.7 1.5 1.5 3 2.2 4.5.2.5.3 1.1.4 1.6.1.6-.2 1.3.5 1.8.2.1 0 .7 0 1.3zm41.5.1c0 4.4 0 8.8-.1 13.3-4.6.2-8.9.2-13.3.2 0-4.4 0-8.8.1-13.3 4.6-.2 8.9-.2 13.3-.2zm-145.6.7c0 3.9 0 7.9-.1 12-4.6.2-9 .2-13.4.2 0-1.9 0-3.7.2-5.6.3-2 0-4.1.3-6.3 4.5-.2 8.8-.2 13.1-.2zm145.6 33.2c-4.4.2-8.8.2-13.3.2-.2-3.8-.2-7.6 0-11.6 4.6-.2 8.9-.2 13.3-.2v11.6zM16.7 45c4.5-.1 9.1-.1 13.7-.1.7 0 1.5.2 1.9-.8 1.3 3 2.5 6.2 3.6 9.5-1.2.2-2.2.4-3.2.4h-7c-.6-1.3-1.1-2.6-1.7-4.1-.2-.4-.3-.6-.5-.7v.7c0 1.1 0 2.3-.2 3.6-2.4.2-4.5.2-6.7.2 0-1.5.2-3 .2-4.6 0-1.3 0-2.6-.2-4.1zm144.6 13.4H148c-.2-3.5-.2-7-.2-10.6 4.4-.2 8.8-.2 13.3 0 .2 3.6.1 7.1.1 10.6zm-50.9 1.2c-4.3.2-8.6.2-13.1.2-.2-3.2-.2-6.3-.2-9.7.1-.4.2-.5.2-.7.9-.1 1.7-.3 2.6-.3h7.5c.9 0 1.8.2 2.8.3.2 3.4.2 6.8.2 10.3zm-94.5 64.2c2.4-.1 4.8-.1 7.4-.1.1 3.6.1 7.2.1 11H1.2v-2.1c1.5-.1 2.9-.3 4.4-.4 2-.2 4-.4 6-1.3 3-1.4 4.2-3.8 4.4-7zm36.3-30.2c-.6.1-1.3.1-1.9.1h-8.5c-1.9-4.3-3.7-8.6-5.3-13 2.6-.1 5 0 7.5-.1.9 0 1.9-.2 2.8-.3 1.2 3 2.5 6.1 3.7 9.1.6 1.4 1.1 2.7 1.6 4.2zm59.2 34.2c.7.7 1.3 1.5 2.1 2.1 2 1.4 4.4 1.8 6.7 2 1.6.1 3.2.3 4.9.4v2.1H96.7c-.1-1.9 0-3.7.1-5.8 4.2-.2 8.2-.2 12.2-.1.9 0 1.7 0 2.3-.7zm144.5-68.9c-4.4.2-8.8.2-13.3.2-.2-3.2-.2-6.3-.2-9.6 4.4-.2 8.8-.2 13.3-.2.2 3.2.2 6.3.2 9.6zM52.6 121.8c.9-.2 1.8-.4 2.6-.4h12.3c-.8 2.4-1.7 4.7-2.6 7.1-.6 1.6-1.3 3.2-1.9 4.8-.3.8-.7 1.3-1.7 1.2h-3.5c-1.7-4.1-3.4-8.3-5.1-12.6zM23.5 95.3c-1.4 0-2.8-.1-4.1-.2-.9 0-1.9.2-2.9.3-.2-4.7-.2-9.5-.2-14.2 0-.6.2-1.3.5-2.1 2.3-.1 4.5-.1 6.7-.1v16.3zm87.9 32.3c-.7.7-1.5.8-2.3.8H96.9c-.2-2.5-.2-5-.2-7.6 4.4-.2 8.9-.2 13.4-.2.2.5.3 1 .2 1.5-.2 1.9.3 3.6 1 5.5zM39.3 61.7c.5 1 1 2.1 1.4 3.1.9 2.2 1.8 4.5 2.6 6.9-2.8.1-5.5 0-8.2.1-.8 0-1.5.2-2.3.4-1.3-3.2-2.5-6.4-3.7-9.8.4-.3.7-.4 1.1-.4 3-.1 6.1-.2 9.1-.3zM110.4 33c-1.3.1-2.6.1-4 .1-.2-3.6-.1-7.1-.2-10.7 0-1.6-.4-3.1-.6-4.7 0-.5 0-1-.1-1.7 0-.3-.1-.4-.2-.4-.1-.9-.3-1.8-.2-2.9 2-.1 3.9-.1 5.7-.1v17.1c0 1-.2 2.1-.4 3.3zm94.5 48.3c-.8-.1-1.4-.4-2-.4H188c-.7 0-1.5-.3-2.3-.7 2.3-1.2 4.3-2.7 6.4-4.3 1.2.1 2.2.3 3.3.3 4.8 0 9.6 0 14.4.1-.9 1.1-1.8 2.3-2.7 3.4-.6.7-.9 1.5-2.1 1.6zM39.3 61.6c-3 .2-6.1.3-9.1.5-.3 0-.6.1-1.1.3-1-2-1.9-4.1-2.8-6.2-.2-.6-.4-1.2-.7-2 2.3-.1 4.7 0 7-.1 1 0 2.1-.2 3.3-.3.9 1.8 1.7 3.6 2.4 5.4.3.8.6 1.6 1 2.6zm41.9 12.3c.8-1.7 1.5-3.4 2.3-5.1 1.3-2.9 2.5-5.8 3.8-8.6 0-.2.2-.4.6-.7 1.8 0 3.4.2 5 .3-1 2.6-2 5.1-3.1 7.7-.9 2.2-1.9 4.5-3 6.7-2 0-3.8-.2-5.5-.3zm-2.6 20.2c-.7 0-1.2-.4-1.8-.4h-4.3c1.2-3.2 2.6-6.4 4-9.6.7-1.5 1.3-3 2.1-4.5.7.8 1.6.5 2.3.6 1 .1 2-.3 2.9.5-.6 1.5-1.1 3.1-1.7 4.6-1.1 2.9-2.3 5.8-3.6 8.8zm131.2-17.8h-14.5c-1 0-2.1-.2-3.2-.4.8-1.4 1.6-2.7 2.5-3.9h17.7c-.5 1.6-1.4 3-2.5 4.4zM16.7 72.5c0-3.6 0-7.2.1-11 2.4-.2 4.5-.2 6.7-.2 0 3.6 0 7.2-.1 11-2.4.2-4.5.2-6.7.2zm225.7.2c4.4-.2 8.8-.2 13.3-.2.2 1.6.2 3.2 0 4.9-4.6.2-8.9.2-13.3.2v-4.9zm-94.2 0c4.3-.2 8.6-.2 13 0 .2 1.7.1 3.3 0 4.9h-12.9v-4.9zM96.8 72c4.4-.2 8.9-.2 13.4-.2.2 1.2.2 2.5 0 3.9-4.6.2-9 .2-13.4.2V72zm0 4c4.4-.1 8.9-.1 13.4-.1.2 1.2.2 2.5.2 3.9-4.4.1-8.8.1-13.4 0-.2-1.2-.2-2.5-.2-3.8zm-4-16.4c-1.6 0-3.2-.1-5-.3.5-1.7 1.3-3.4 2-5.1.8-1.8 1.5-3.7 2.4-5.6 1.5.2 2.8.4 4.3.7.1.2.1.4.1.5-.5 1.4-1 2.9-1.5 4.3-.7 1.8-1.5 3.5-2.3 5.4zm-69.3 1.7c-2.2.2-4.4.2-6.7.2-.2-2.5-.2-5-.2-7.6 2.2-.2 4.4-.2 6.7-.2.2 2.5.2 5 .2 7.6zm218.9 16.4c4.4-.2 8.8-.2 13.3-.2.2 1.1.2 2.3.2 3.6-4.4.2-8.8.1-13.3.2-.2-1.1-.2-2.3-.2-3.6zm-94.2 0c4.3-.2 8.6-.2 13 0 .2 1.3.1 2.4.1 3.6h-12.9c-.2-1.1-.2-2.3-.2-3.6zM32.9 72.4c.7-.2 1.5-.5 2.2-.5h8.2c.7 1.4 1.3 2.8 1.7 4.3-3.6.1-6.9.1-10.3.1-.5-1.1-.9-2.2-1.4-3.2 0-.2-.2-.3-.4-.6zm1.8 4c3.4-.1 6.8-.1 10.3-.1.7 1.3 1.3 2.5 1.8 3.9-.9.2-1.9.4-2.8.4h-7.5c-.7-1.4-1.3-2.7-1.9-4.2zm-18.1-3.7c2.2-.2 4.4-.2 6.7-.2.2 2 .2 4.1.2 6.2-2.2.2-4.4.2-6.7.2-.2-2-.2-4.1-.2-6.2zm94.1-60.2c-1.9.1-3.7.1-5.7.1-.5-2.1-.9-4.1-1.2-6.2-.2-1.2-.2-2.5 0-3.8.4.4.8.7 1.1 1.1 1.7 2.2 3.4 4.5 5.1 6.7.4.5.5 1.3.7 2.1zM84 80.7c-1-.7-2-.3-3-.4-.8 0-1.6.2-2.2-.7.7-1.9 1.4-3.7 2.3-5.6 1.8 0 3.6 0 5.5.2-.7 2.2-1.6 4.3-2.5 6.5zM23.6 49.9c-.1-.2-.1-.5-.1-.7.1.2.3.3.4.6 0 0-.1 0-.3.1zm73.2 0c-.1-.1-.1-.3-.1-.5.1-.1.3-.1.6-.1v.6h-.4zm8.4-34.2c.1 0 .2 0 .2.2 0 0-.2 0-.2-.2zM29.9 37.4s-.1 0-.2-.2c0 0 .2 0 .2.2zm-23.4.5h.2s-.1 0-.1-.2zm24 1.2s-.1 0-.2-.2c0 0 .2 0 .2.2zm.7 1.7s-.1 0-.2-.1c0 0 .2 0 .2.1zm1 2.7s-.1 0-.2-.1c0 0 .2 0 .2.1zm61 2.8v.2c-.1 0 0-.1 0-.2zm-.6 1.7v0z"
        fill="url(#linear-gradient)"
      />
    </Svg>
  );
}

export default LogoSvg;
