/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/storage/*": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * route
         * @description
         *
         *      __ - ****
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         *  (handle)
         * @description
         *
         *      _app/controllers/home_controller.ts_ - **handle**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         *  (handle)
         * @description
         *
         *      _app/controllers/health_checks_controller.ts_ - **handle**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         *  (register)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **register**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         *  (login)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **login**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/confirm/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         *  (confirm)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **confirm**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/forgot-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         *  (passwordForgot)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **passwordForgot**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/reset-password/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         *  (passwordReset)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **passwordReset**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/logout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         *  (logout)
         * @description
         *
         *      _app/controllers/auth_controller.ts_ - **logout**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/brands": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of brands (index)
         * @description
         *
         *      _app/controllers/brands_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/perfumes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of perfumes (index)
         * @description
         *
         *      _app/controllers/perfumes_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/perfumes/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of perfumes (show)
         * @description
         *
         *      _app/controllers/perfumes_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/perfumes/{id}/similar": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         *  (similar)
         * @description
         *
         *      _app/controllers/perfumes_controller.ts_ - **similar**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/notes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of notes (index)
         * @description
         *
         *      _app/controllers/notes_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/notes/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of notes (show)
         * @description
         *
         *      _app/controllers/notes_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of profile (index)
         * @description
         *
         *      _app/controllers/profile_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update profile (update)
         * @description
         *
         *      _app/controllers/profile_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete profile (destroy)
         * @description
         *
         *      _app/controllers/profile_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/settings/cache": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         *  (cache_flush)
         * @description
         *
         *      _app/controllers/admin/admin_controller.ts_ - **cache_flush**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/stats_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/users_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        /**
         *  (store)
         * @description
         *
         *      _app/controllers/admin/users_controller.ts_ - **store**
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/users/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/users_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/users_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/users_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/brands": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/brands_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/brands/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/brands_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/brands_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/brands_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/noses": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/noses_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/noses/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/noses_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/noses_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/noses_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/notes/categories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/note_categories_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/notes/categories/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/note_categories_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/note_categories_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/note_categories_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/notes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/notes_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/notes/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/notes_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/notes_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/notes_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/perfumes_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/perfumes_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/perfumes_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/perfumes_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{perfume_id}/variants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/perfume_variants_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{perfume_id}/variants/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a single instance of admin (show)
         * @description
         *
         *      _app/controllers/admin/perfume_variants_controller.ts_ - **show**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/perfume_variants_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete admin (destroy)
         * @description
         *
         *      _app/controllers/admin/perfume_variants_controller.ts_ - **destroy**
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{perfume_id}/noses": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/perfume_noses_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/perfume_noses_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{perfume_id}/notes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/perfume_notes_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update admin (update)
         * @description
         *
         *      _app/controllers/admin/perfume_notes_controller.ts_ - **update**
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/perfumes/{perfume_id}/histories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a list of admin (index)
         * @description
         *
         *      _app/controllers/admin/perfume_histories_controller.ts_ - **index**
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    perfume_id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Returns **401** (Unauthorized) */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Returns **403** (Forbidden) */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/jobs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * route
         * @description
         *
         *      __ - ****
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/jobs/trpc/*": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * route
         * @description
         *
         *      __ - ****
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": unknown;
                };
            };
            responses: {
                /** @description No Content */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/jobs/*": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * route
         * @description
         *
         *      __ - ****
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OK */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": unknown;
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** @description Any JSON object not defined as schema */
        Any: unknown;
        PaginationMeta: {
            /** @example 100 */
            total?: number;
            /** @example 2 */
            page?: number;
            /** @example 10 */
            perPage?: number;
            /** @example 3 */
            currentPage?: number;
            /** @example 10 */
            lastPage?: number;
            /** @example 1 */
            firstPage?: number;
            /** @example /?page=10 */
            lastPageUrl?: string;
            /** @example /?page=1 */
            firstPageUrl?: string;
            /** @example /?page=6 */
            nextPageUrl?: string;
            /** @example /?page=5 */
            previousPageUrl?: string;
        };
        /** @description Attachment (Interface) */
        Attachment: {
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            originalName?: string;
            /** @example 962 */
            size?: number;
            /** @example Lorem Ipsum */
            extname?: string;
            /** @example Lorem Ipsum */
            mimeType?: string;
            /** @example https://example.com */
            url?: string;
        };
        /** @description Brand (Model) */
        Brand: {
            /** @example 641 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            display_name?: string;
            /** @example Lorem ipsum dolor sit amet */
            description?: string;
            /** @example null */
            logo?: components["schemas"]["Attachment"];
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
        };
        /** @description Nose (Model) */
        Nose: {
            /** @example 872 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            display_name?: string;
            /** @example Lorem ipsum dolor sit amet */
            description?: string;
            /** @example https://example.com/avatar.png */
            avatar?: components["schemas"]["Attachment"];
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            perfumes?: components["schemas"]["Perfume"][];
        };
        /** @description Note (Model) */
        Note: {
            /** @example 5 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            display_name?: string;
            /** @example 488 */
            category_id?: number;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            /** @example null */
            category?: components["schemas"]["NoteCategory"];
            perfumes?: components["schemas"]["Perfume"][];
        };
        /** @description NoteCategory (Model) */
        NoteCategory: {
            /** @example 808 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            display_name?: string;
            /** @example null */
            cover?: components["schemas"]["Attachment"];
            /** @example null */
            icon?: components["schemas"]["Attachment"];
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
        };
        /** @description Perfume (Model) */
        Perfume: {
            /** @example 697 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /** @example Lorem Ipsum */
            display_name?: string;
            /** @example Lorem ipsum dolor sit amet */
            description?: string;
            /**
             * @example hesperides
             * @enum {string}
             */
            family?: "hesperides" | "floral" | "woody" | "fern" | "chypre" | "oriental" | "aromatic";
            /**
             * @example parfum
             * @enum {string}
             */
            type?: "parfum" | "EXDP" | "EDP" | "EDT" | "EDC" | "EF";
            /**
             * @example M
             * @enum {string}
             */
            gender?: "M" | "F" | "all";
            /** @example 2023 */
            year?: string;
            /**
             * @example cold
             * @enum {string}
             */
            season?: "cold" | "warm" | "cold_warm";
            chems?: string[];
            /** @example true */
            is_featured?: boolean;
            /** @example null */
            picture?: components["schemas"]["Attachment"];
            /** @example 474 */
            brand_id?: number;
            /** @example 3 */
            family_id?: number;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            deleted_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            /** @example null */
            brand?: components["schemas"]["Brand"];
            variants?: components["schemas"]["PerfumeVariant"][];
            histories?: components["schemas"]["PerfumeHistory"][];
            noses?: components["schemas"]["Nose"][];
            notes?: components["schemas"]["Note"][];
        };
        /** @description PerfumeHistory (Model) */
        PerfumeHistory: {
            /** @example 913 */
            id?: number;
            /** @example null */
            data?: components["schemas"]["Any"];
            /** @example 120 */
            perfume_id?: number;
            /** @example 125 */
            provider_id?: number;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            /** @example null */
            perfume?: components["schemas"]["Perfume"];
            /** @example null */
            provider?: components["schemas"]["Provider"];
        };
        /** @description PerfumeVariant (Model) */
        PerfumeVariant: {
            /** @example 216 */
            id?: number;
            /** @example Lorem Ipsum */
            sku?: string;
            /** @example 291 */
            size?: number;
            /** @example true */
            is_sampling?: boolean;
            /** @example true */
            in_stock?: boolean;
            /** @example 10.5 */
            price?: number;
            /** @example Lorem Ipsum */
            currency?: string;
            /** @example Lorem Ipsum */
            vendor?: string;
            /** @example Lorem Ipsum */
            link?: string;
            /** @example 623 */
            perfume_id?: number;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            /** @example null */
            perfume?: components["schemas"]["Perfume"];
        };
        /** @description Provider (Model) */
        Provider: {
            /** @example 843 */
            id?: number;
            /** @example John Doe */
            name?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
            histories?: components["schemas"]["PerfumeHistory"][];
        };
        /** @description User (Model) */
        User: {
            /** @example 748 */
            id?: number;
            /**
             * @example user
             * @enum {string}
             */
            role?: "user" | "admin";
            /**
             * @example F
             * @enum {string}
             */
            gender?: "F" | "M";
            /** @example Lorem Ipsum */
            username?: string;
            /**
             * Format: date
             * @example 2021-03-23
             */
            birthdate?: string;
            /** @example Lorem Ipsum */
            phone?: string;
            /**
             * Format: email
             * @example <EMAIL>
             */
            email?: string;
            /** @example United States of America */
            country?: string;
            /** @example Chicago */
            city?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            created_at?: string;
            /**
             * Format: date-time
             * @example 2021-03-23T16:13:08.489+01:00
             */
            updated_at?: string;
        };
    };
    responses: {
        /** @description Access token is missing or invalid */
        Forbidden: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description The request was accepted */
        Accepted: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description The resource has been created */
        Created: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description The resource has been created */
        NotFound: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
        /** @description The resource has been created */
        NotAcceptable: {
            headers: {
                [name: string]: unknown;
            };
            content?: never;
        };
    };
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;

export interface PerfumeNote {
  id: string;
  perfumeId: number;
  userId: number;
  content: string;
  createdAt: string;
  updatedAt: string;
}
