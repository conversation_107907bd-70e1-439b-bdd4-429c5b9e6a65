{"expo": {"name": "Mon parfum idéal", "slug": "mon-parfum-ideal", "owner": "ledhcg", "version": "1.0.6", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "mpi", "userInterfaceStyle": "automatic", "newArchEnabled": true, "platforms": ["ios", "android"], "ios": {"supportsTablet": false, "bundleIdentifier": "fr.lunify.mpi", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "CFBundleLocalizations": ["fr", "en"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#000000"}, "package": "fr.lunify.mpi"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "backgroundColor": "#000000", "imageWidth": 250}], "expo-localization", ["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}}], "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "ad4617d9-2e18-424d-9eb4-5ba55f4a4bd2"}, "router": {"origin": false}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/ad4617d9-2e18-424d-9eb4-5ba55f4a4bd2"}}}