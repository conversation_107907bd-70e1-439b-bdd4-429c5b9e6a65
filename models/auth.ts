import { createModel } from "@rematch/core";
import { RootModel } from ".";
import { components } from "@/@types/api.types";
import axiosInstance from "@/api/axios";
import asyncStorage from "@react-native-async-storage/async-storage";

export interface IAuthResponse {
  access_token: string;
  user: components["schemas"]["User"];
}

export interface ILoginParams {
  email: string;
  password: string;
}

export interface IRegisterParams {
  gender?: "F" | "M";
  username: string;
  birthdate?: string;
  phone?: string;
  country: string;
  city?: string;
  email: string;
  password: string;
}

export interface IForgotPasswordParams {
  email: string;
}

export interface IResetPasswordParams {
  id: string;
  password: string;
}

export interface IAuthState {
  user: components["schemas"]["User"] | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export const auth = createModel<RootModel>()({
  state: {
    user: null,
    isAuthenticated: false,
    isLoading: false,
  } as IAuthState,
  reducers: {
    setLoading(state, payload: boolean) {
      return { ...state, isLoading: payload };
    },
    changeAuthenticatedStatus(state, payload: boolean) {
      return { ...state, isAuthenticated: payload };
    },
    updateUser(state, payload: components["schemas"]["User"] | null) {
      return { ...state, user: payload };
    },
  },
  effects: (dispatch) => ({
    async reset() {
      await asyncStorage.removeItem("access-token");
      this.changeAuthenticatedStatus(false);
      this.updateUser(null);
    },
    async refreshUser(_payload, _rootState) {
      try {
        this.setLoading(true);
        const accessToken = await asyncStorage.getItem("access-token");
        if (accessToken) {
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
          const { data } = await axiosInstance.get("/profile");
          if (data) {
            this.changeAuthenticatedStatus(true);
            this.updateUser(data);
            return;
          }
        }
        await dispatch.auth.reset();
      } catch (e) {
        console.error(e);
        await dispatch.auth.reset();
      } finally {
        this.setLoading(false);
      }
    },
    async forgotPassword(payload: IForgotPasswordParams, _rootState) {
      try {
        const { data } = await axiosInstance.post(
          "/auth/forgot-password",
          payload,
        );
        return data;
      } catch (e) {
        throw e;
      }
    },
    async resetPassword(payload: IResetPasswordParams, _rootState) {
      try {
        const { data } = await axiosInstance.post(
          `/auth/reset-password/${payload.id}`,
          { password: payload.password },
        );
        return data;
      } catch (e) {
        throw e;
      }
    },
    async login(payload: ILoginParams, _rootState) {
      try {
        this.setLoading(true);
        const { data } = await axiosInstance.post("/auth/login", payload);
        if (data) {
          await asyncStorage.setItem("access-token", data.token.value);
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${data.token.value}`;
          this.changeAuthenticatedStatus(true);
          this.updateUser(data.user);
        }
        return data;
      } catch (e) {
        throw e;
      } finally {
        this.setLoading(false);
      }
    },
    async register(payload: IRegisterParams, _rootState) {
      try {
        this.setLoading(true);
        const { data } = await axiosInstance.post("/auth/register", payload);
        return data;
      } catch (e) {
        throw e;
      } finally {
        this.setLoading(false);
      }
    },
    async logout() {
      try {
        this.setLoading(true);
        const { data } = await axiosInstance.post("/auth/logout");
        await dispatch.auth.reset();
        return data;
      } catch (e) {
        throw e;
      } finally {
        this.setLoading(false);
      }
    },
    async delete() {
      try {
        this.setLoading(true);
        const { data } = await axiosInstance.delete("/profile");
        await dispatch.auth.reset();
        return data;
      } catch (e) {
        throw e;
      } finally {
        this.setLoading(false);
      }
    },
  }),
});
