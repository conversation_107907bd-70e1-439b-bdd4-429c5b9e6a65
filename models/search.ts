import { createModel } from "@rematch/core";
import { RootModel } from "@/models/index";
import { Filters } from "@/components/search/SearchByCriterias";

export const DEFAULT_FILTERS: Filters = {
  family: undefined,
  note_top: undefined,
  note_middle: undefined,
  note_base: undefined,
  price_min: 0,
  price_max: undefined,
  gender: [],
  type: "",
  size_min: 0,
  size_max: undefined,
  year: undefined,
};

export const search = createModel<RootModel>()({
  state: {
    ...DEFAULT_FILTERS,
    activeSubTab: "simple" as "simple" | "advanced",
  },
  reducers: {
    addFilters(state, payload: Record<string, any>) {
      return { ...state, ...payload };
    },
    removeFilters(_state, _) {
      return {
        ...DEFAULT_FILTERS,
        activeSubTab: "simple" as "simple" | "advanced",
      };
    },
    setActiveSubTab(state, payload: "simple" | "advanced") {
      return { ...state, activeSubTab: payload };
    },
  },
});
