import { createModel } from "@rematch/core";
import { RootModel } from "@/models/index";
import asyncStorage from "@react-native-async-storage/async-storage";
import { PerfumeNote } from "@/@types/api.types";

export const favorites = createModel<RootModel>()({
  state: {
    perfumeIds: [] as number[],
    notes: [] as PerfumeNote[],
  },
  selectors: (slice, createSelector, hasProps) => ({
    ids() {
      return slice((state) => state.perfumeIds);
    },
    getNoteByPerfumeId: () => (perfumeId: number) => {
      return slice((state) => state.notes.find((note) => note.perfumeId === perfumeId));
    },
    getAllNotes() {
      return slice((state) => state.notes);
    },
  }),
  reducers: {
    setFavorites(state, payload: number[]) {
      return { ...state, perfumeIds: payload };
    },
    addFavorite(state, payload: number) {
      const tmp = [...state.perfumeIds, payload];
      asyncStorage.setItem("favorites", JSON.stringify(tmp));
      return { ...state, perfumeIds: tmp };
    },
    removeFavorite(state, payload: number) {
      const tmp = state.perfumeIds.filter((item) => item !== payload);
      const tmpNotes = state.notes.filter((note) => note.perfumeId !== payload);
      asyncStorage.setItem("favorites", JSON.stringify(tmp));
      asyncStorage.setItem("perfumeNotes", JSON.stringify(tmpNotes));
      return {
        ...state,
        perfumeIds: tmp,
        notes: tmpNotes,
      };
    },
    setNotes(state, payload: PerfumeNote[]) {
      return { ...state, notes: payload };
    },
    addNote(state, payload: PerfumeNote) {
      const tmp = [...state.notes.filter((note) => note.perfumeId !== payload.perfumeId), payload];
      asyncStorage.setItem("perfumeNotes", JSON.stringify(tmp));
      return { ...state, notes: tmp };
    },
    updateNote(state, payload: PerfumeNote) {
      const tmp = state.notes.map((note) => note.id === payload.id ? payload : note);
      asyncStorage.setItem("perfumeNotes", JSON.stringify(tmp));
      return { ...state, notes: tmp };
    },
    deleteNote(state, payload: string) {
      const tmp = state.notes.filter((note) => note.id !== payload);
      asyncStorage.setItem("perfumeNotes", JSON.stringify(tmp));
      return { ...state, notes: tmp };
    },
  },
  effects: (dispatch) => ({
    toggleFavorite(id: number, rootState) {
      if (rootState.favorites.perfumeIds.includes(id)) {
        dispatch.favorites.removeFavorite(id);
      } else {
        dispatch.favorites.addFavorite(id);
      }
    },
    async loadFavorites() {
      const data = await asyncStorage.getItem("favorites");
      if (data) {
        dispatch.favorites.setFavorites(JSON.parse(data));
      }
    },
    async loadNotes() {
      const data = await asyncStorage.getItem("perfumeNotes");
      if (data) {
        dispatch.favorites.setNotes(JSON.parse(data));
      }
    },
    async saveNote(payload: { perfumeId: number; content: string; userId: number }) {
      const noteId = `note_${payload.perfumeId}_${payload.userId}_${Date.now()}`;
      const note: PerfumeNote = {
        id: noteId,
        perfumeId: payload.perfumeId,
        userId: payload.userId,
        content: payload.content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch.favorites.addNote(note);
    },
    async editNote(payload: { id: string; content: string }, rootState) {
      const existingNote = rootState.favorites.notes.find((note) => note.id === payload.id);
      if (existingNote) {
        const updatedNote: PerfumeNote = {
          ...existingNote,
          content: payload.content,
          updatedAt: new Date().toISOString(),
        };
        dispatch.favorites.updateNote(updatedNote);
      }
    },
    async removeNote(noteId: string) {
      dispatch.favorites.deleteNote(noteId);
    },
    async fetchPerfumes() {},
  }),
});
