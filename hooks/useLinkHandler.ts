import { useState, useRef, useCallback } from "react";
import { Linking, Platform } from "react-native";
import { openBrowserAsync } from "expo-web-browser";
import { useI18n } from "@/providers/i18n/i18n.provider";

interface LinkHandlerOptions {
  useInAppBrowser?: boolean;
  loadingDelay?: number;
  resetDelay?: number;
  cacheDuration?: number;
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export function useLinkHandler(options: LinkHandlerOptions = {}) {
  const {
    useInAppBrowser = false,
    loadingDelay = 100,
    resetDelay = 100,
    cacheDuration = 60000, // 60 seconds cache by default
    onSuccess,
    onError,
  } = options;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [activeLink, setActiveLink] = useState<string | null>(null);
  const recentlyVisitedUrls = useRef<Map<string, number>>(new Map());
  const pendingLinks = useRef<Set<string>>(new Set());
  const { t } = useI18n();

  // Function to check if URL has been recently visited
  const hasRecentlyVisited = useCallback(
    (url: string): boolean => {
      if (!recentlyVisitedUrls.current.has(url)) {
        return false;
      }

      const visitTime = recentlyVisitedUrls.current.get(url) || 0;
      const now = Date.now();

      // Check if URL is in cache and the most recent visit time < cacheDuration
      return now - visitTime < cacheDuration;
    },
    [cacheDuration]
  );

  // Function to clean up expired URLs from cache
  const cleanupExpiredUrls = useCallback(() => {
    const now = Date.now();
    recentlyVisitedUrls.current.forEach((visitTime, url) => {
      if (now - visitTime > cacheDuration) {
        recentlyVisitedUrls.current.delete(url);
      }
    });
  }, [cacheDuration]);

  const openLink = useCallback(
    async (url: string, identifier?: string) => {
      if (!url) return;

      // If URL is already being processed, avoid calling again
      if (pendingLinks.current.has(url)) {
        return;
      }

      // If URL has been visited recently, open immediately
      if (hasRecentlyVisited(url)) {
        Linking.openURL(url).catch((err) =>
          console.error("Error opening cached URL:", err)
        );
        return;
      }

      // Mark URL as being processed
      pendingLinks.current.add(url);

      // Set loading state
      setIsLoading(true);
      if (identifier) setActiveLink(identifier);

      try {
        // Clean up expired URLs before adding a new URL
        cleanupExpiredUrls();

        // Use setTimeout with reduced delay to improve user experience
        setTimeout(async () => {
          try {
            // Use in-app browser if requested and not on web
            if (useInAppBrowser && Platform.OS !== "web") {
              await openBrowserAsync(url);
            } else {
              await Linking.openURL(url);
            }

            // Save URL to cache
            recentlyVisitedUrls.current.set(url, Date.now());

            onSuccess?.();
          } catch (error) {
            console.error("Error opening URL:", error);
            onError?.(error);
          } finally {
            // Remove URL from processing list
            pendingLinks.current.delete(url);

            // Reset state after opening the link
            setTimeout(() => {
              setIsLoading(false);
              setActiveLink(null);
            }, resetDelay);
          }
        }, loadingDelay);
      } catch (error) {
        console.error("Error in link handler:", error);
        pendingLinks.current.delete(url);
        setIsLoading(false);
        setActiveLink(null);
        onError?.(error);
      }
    },
    [
      loadingDelay,
      resetDelay,
      hasRecentlyVisited,
      cleanupExpiredUrls,
      useInAppBrowser,
      onSuccess,
      onError,
    ]
  );

  const isLinkActive = useCallback(
    (identifier: string) => activeLink === identifier,
    [activeLink]
  );

  return {
    openLink,
    isLoading,
    isLinkActive,
    redirectingText: t("perfume.redirecting"),
    buyNowText: t("perfume.buy_now"),
  };
}
