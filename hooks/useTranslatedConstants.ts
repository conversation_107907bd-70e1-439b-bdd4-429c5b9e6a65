import { useEffect, useState, useMemo, useCallback } from "react";
import {
  createTranslatedConstants,
  getFamilyLabel as getFamilyLabelFunc,
  FAMILIES as FAMILIES_CONST,
} from "@/constants/constants";
import { i18nEvents, LANGUAGE_CHANGE_EVENT } from "@/utils/i18n";

export function useTranslatedConstants() {
  const [constants, setConstants] = useState(createTranslatedConstants());

  useEffect(() => {
    // Update constants when language changes
    const handleLanguageChange = () => {
      setConstants(createTranslatedConstants());
    };

    // Listen for language change events
    i18nEvents.on(LANGUAGE_CHANGE_EVENT, handleLanguageChange);

    // Clean up event listener
    return () => {
      i18nEvents.off(LANGUAGE_CHANGE_EVENT, handleLanguageChange);
    };
  }, []);

  // Tạo phiên bản động của hàm getGenderLabel sử dụng giá trị constants hiện tại
  const getGenderLabel = useCallback((key: string): string => {
    return constants.GENDER_LABELS[key] || "Inconnu";
  }, [constants.GENDER_LABELS]);

  // Tạo phiên bản động của hàm getFamilyLabel sử dụng giá trị constants hiện tại
  const getFamilyLabel = useCallback((key: string): string => {
    return constants.FAMILY_LABELS[key] || "Inconnu";
  }, [constants.FAMILY_LABELS]);

  // Memoize kết quả trả về để tránh tạo object mới mỗi khi component re-render
  return useMemo(() => ({
    ...constants,
    getGenderLabel,
    getFamilyLabel,
    FAMILIES: FAMILIES_CONST,
  }), [constants, getGenderLabel, getFamilyLabel]);
}
