{"name": "mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "test": "jest --watchAll", "lint": "expo lint", "generate:types": "openapi-typescript http://localhost:3001/swagger --output ./@types/api.types.ts", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/overpass": "^0.2.3", "@expo-google-fonts/playfair-display": "^0.2.3", "@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@gorhom/bottom-sheet": "^5.0.6", "@hookform/resolvers": "^3.9.1", "@legendapp/motion": "^2.4.0", "@react-native-aria/interactions": "^0.2.15", "@react-native-aria/overlays": "^0.3.15", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.0.14", "@react-navigation/native": "^7.0.14", "@rematch/core": "^2.2.0", "@rematch/loading": "^2.1.2", "@rematch/select": "^3.1.2", "@salmonco/react-native-radar-chart": "^0.0.26", "axios": "^1.7.9", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "^53.0.17", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "^16.1.6", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.16", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.1", "lucide-react-native": "^0.525.0", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.53.2", "react-native": "0.79.5", "react-native-awesome-slider": "^2.6.6", "react-native-color-matrix-image-filters": "^7.0.2", "react-native-css-interop": "^0.1.22", "react-native-deck-swiper": "^2.0.17", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.1.2", "redux": "^5.0.1", "slugify": "^1.6.6", "sonner-native": "^0.16.2", "swr": "^2.2.5", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "use-debounce": "^10.0.4", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.25.9", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.1", "eslint": "^8.57.1", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.2.2", "jest-expo": "~53.0.9", "jscodeshift": "^0.15.2", "openapi-typescript": "^7.4.4", "prettier": "^3.4.2", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true}