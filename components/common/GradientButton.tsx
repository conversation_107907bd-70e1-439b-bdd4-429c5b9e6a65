import { LinearGradient } from "@/components/ui/linear-gradient";
import { ITextProps, Text } from "@/components/ui/text";
import React, { FC, ReactNode } from "react";
import { TouchableOpacity, TouchableOpacityProps } from "react-native";
import { HStack } from "../ui/hstack";
import { Spinner } from "../ui/spinner";

interface GradientButtonProps extends TouchableOpacityProps {
  children: ReactNode | string;
  buttonTextProps?: ITextProps;
  loading?: boolean;
}

const GradientButton: FC<GradientButtonProps> = ({
  children,
  buttonTextProps,
  loading,
  ...props
}) => {
  return (
    <TouchableOpacity
      onPress={props.onPress}
      activeOpacity={0.6}
      {...props}
      className={`h-[44px] ${props.className}`}
    >
      <LinearGradient
        colors={["#F1CA90", "#BF9E57"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className={`items-center justify-center p-3 ${props.className}`}
      >
        <HStack space="sm">
          {loading && <Spinner color="black" />}
          <Text
            className="uppercase text-base font-normal text-black"
            {...buttonTextProps}
          >
            {children}
          </Text>
        </HStack>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default GradientButton;
