import React, { <PERSON> } from "react";
import { Heading } from "../ui/heading";
import BackArrow from "@/assets/images/icons/back_arrow.svg";
import { Pressable } from "../ui/pressable";
import { router } from "expo-router";
import { View } from "react-native";

interface BackButtonProps {
  title: string;
  onPress?: VoidFunction;
}

const BackButton: FC<BackButtonProps> = ({ title, onPress }) => {
  return (
    <View className="flex items-start">
      <Pressable
        className="mb-10 gap-2"
        onPress={onPress ? onPress : () => router.back()}
      >
        <BackArrow />
        <Heading size="xl" className="text-serif text-gold-ultralight">
          {title}
        </Heading>
      </Pressable>
    </View>
  );
};

export default BackButton;
