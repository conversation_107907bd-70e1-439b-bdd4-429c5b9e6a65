import React, { FC, useState } from "react";
import { TextInputProps } from "react-native";
import { FieldError, FieldValues } from "react-hook-form";
import { Input, InputField, InputIcon, InputSlot } from "../ui/input";
import { AlertCircleIcon, EyeIcon, EyeOffIcon } from "../ui/icon";
import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";

export interface AppPasswordInputProps extends TextInputProps, FieldValues {
  title: string;
  placeholder: string;
  error?: FieldError;
  containerClassName?: string;
}

const AppPasswordInput: FC<AppPasswordInputProps> = ({
  title,
  placeholder,
  error,
  containerClassName,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const handleState = () => {
    setShowPassword((showState) => !showState);
  };

  return (
    <FormControl
      isInvalid={Boolean(error)}
      className={`bg-grey-card p-5 py-7 ${containerClassName}`}
    >
      <FormControlLabel>
        <FormControlLabelText
          className={`font-normal text-sm ${error ? "text-error-700" : "text-gold-ultralight"}`}
        >
          {title}
        </FormControlLabelText>
      </FormControlLabel>
      <Input variant="underlined" size="md" className="text-gold-ultralight">
        <InputField
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          autoCapitalize="none"
          {...props}
        />
        <InputSlot className="pr-3" onPress={handleState}>
          <InputIcon
            as={showPassword ? EyeIcon : EyeOffIcon}
            className="text-gold-ultralight"
          />
        </InputSlot>
      </Input>
      <FormControlError>
        {error && (
          <>
            <FormControlErrorIcon as={AlertCircleIcon} />
            <FormControlErrorText>{error.message}</FormControlErrorText>
          </>
        )}
      </FormControlError>
    </FormControl>
  );
};

export default AppPasswordInput;
