import React, { FC, useMemo } from "react";
import { TextInputProps } from "react-native";
import { FieldError, FieldValues } from "react-hook-form";
import { Input, InputField, InputSlot } from "../ui/input";
import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { AlertCircleIcon } from "../ui/icon";
import Pencil from "@/assets/images/icons/pencil.svg";
import Check from "@/assets/images/icons/check.svg";
import { format, parse } from "date-fns";
import { fr } from "date-fns/locale";

export interface AppDateInputProps extends TextInputProps, FieldValues {
  title: string;
  placeholder: string;
  value: string;
  onChange: (e: any) => void;
  error?: FieldError;
  containerClassName?: string;
  manualMode?: boolean;
  onValidate?: any;
}

const AppDateInput: FC<AppDateInputProps> = ({
  title,
  placeholder,
  value,
  onChange,
  error,
  containerClassName,
  manualMode,
  onValidate,
  ...props
}) => {
  const formatToDate = (newValue: string) => {
    const cleaned = newValue.replace(/\D/g, "");

    const day = cleaned.slice(0, 2);
    const month = cleaned.slice(2, 4);
    const year = cleaned.slice(4, 8);

    let formattedDate = "";
    if (day) formattedDate = day;
    if (month) formattedDate += `/${month}`;
    if (year) formattedDate += `/${year}`;

    return formattedDate;
  };

  const handleChange = (text: string) => {
    const formatted = formatToDate(text);
    onChange(formatted);
  };

  const formattedValue = useMemo(() => {
    if (value.length === 10) {
      return format(parse(value, "dd/MM/yyyy", new Date()), "dd LLLL yyyy", {
        locale: fr,
      });
    }
    return value;
  }, [value]);

  return (
    <FormControl
      isInvalid={Boolean(error)}
      className={`bg-grey-card p-5 py-7 ${containerClassName}`}
    >
      <FormControlLabel>
        <FormControlLabelText
          className={`font-normal text-sm ${error ? "text-error-700" : "text-gold-ultralight"}`}
        >
          {title}
        </FormControlLabelText>
      </FormControlLabel>
      <Input variant="underlined" size="md" className="text-gold-ultralight">
        <InputField
          value={manualMode && props.isDirty === false ? formattedValue : value}
          onChangeText={handleChange}
          keyboardType="numeric"
          placeholder={placeholder}
          maxLength={10}
          {...props}
        />
        {manualMode && props.isDirty !== undefined && (
          <InputSlot onPress={onValidate}>
            {props.isDirty === false ? <Pencil /> : <Check stroke="#89E789" />}
          </InputSlot>
        )}
      </Input>
      <FormControlError>
        {error && (
          <>
            <FormControlErrorIcon as={AlertCircleIcon} />
            <FormControlErrorText>{error.message}</FormControlErrorText>
          </>
        )}
      </FormControlError>
    </FormControl>
  );
};

export default AppDateInput;
