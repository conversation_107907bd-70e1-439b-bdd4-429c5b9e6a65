import { useI18n } from "@/providers/i18n/i18n.provider";
import {
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
} from "../ui/alert-dialog";
import { Button, ButtonText } from "../ui/button";
import { Heading } from "../ui/heading";
import { Text } from "../ui/text";
import React from "react";

interface ConfirmDialogProps {
  open: boolean;
  title: string;
  description: string;
  onClose: VoidFunction;
  onConfirm: () => Promise<void>;
}

export default function ConfirmDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
}: ConfirmDialogProps) {
  const { t } = useI18n();
  
  return (
    <AlertDialog isOpen={open} onClose={onClose} size="md">
      <AlertDialogBackdrop />
      <AlertDialogContent>
        <AlertDialogHeader>
          <Heading className="text-gold-ultralight font-semibold" size="md">
            {title}
          </Heading>
        </AlertDialogHeader>
        <AlertDialogBody className="mt-3 mb-4">
          <Text size="sm" className="text-gold-ultralight">
            {description}
          </Text>
        </AlertDialogBody>
        <AlertDialogFooter>
          <Button
            variant="outline"
            action="secondary"
            onPress={onClose}
            size="sm"
          >
            <ButtonText className="text-gold-ultralight">
              {t("profile.cancel")}
            </ButtonText>
          </Button>
          <Button size="sm" onPress={onConfirm} className="bg-error-300">
            <ButtonText className="text-gold-ultralight">
              {t("profile.delete")}
            </ButtonText>
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
