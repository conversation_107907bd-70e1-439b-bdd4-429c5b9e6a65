import { FC, ReactNode } from "react";
import {
  TouchableOpacity,
  TouchableOpacityProps,
  Text,
  TextProps,
} from "react-native";
import { HStack } from "../ui/hstack";
import { Spinner } from "../ui/spinner";

interface AppButtonProps extends TouchableOpacityProps {
  children: ReactNode | string;
  buttonTextProps?: TextProps;
  buttonTextClassName?: string;
  loading?: boolean;
}

const AppButton: FC<AppButtonProps> = ({
  children,
  buttonTextProps,
  buttonTextClassName,
  loading,
  ...props
}) => {
  return (
    <TouchableOpacity
      {...props}
      className={`border border-gold-ultralight px-4 py-2 flex justify-center items-center w-full h-[44px] ${props.className}`}
    >
      <HStack space="sm">
        {loading && <Spinner color="black" />}
        <Text
          {...buttonTextProps}
          className={`text-gold-ultralight text-center uppercase font-normal text-base ${buttonTextClassName}`}
          allowFontScaling={false}
        >
          {children}
        </Text>
      </HStack>
    </TouchableOpacity>
  );
};

export default AppButton;
