import React, { <PERSON> } from "react";
import { Pressable, View } from "react-native";
import { FieldError, FieldValues } from "react-hook-form";
import {
  RadioGroup,
  Radio,
  RadioIndicator,
  RadioIcon,
  RadioLabel,
} from "../ui/radio";
import { HStack } from "../ui/hstack";
import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { AlertCircleIcon } from "../ui/icon";
import Pencil from "@/assets/images/icons/pencil.svg";
import Check from "@/assets/images/icons/check.svg";
import { createIcon, Svg } from "@gluestack-ui/icon";
import { Circle, Path } from "react-native-svg";

const CircleIcon = createIcon({
  Root: Svg,
  viewBox: '0 0 24 24',
  path: (
    <>
      <Circle
        cx="12"
        cy="12"
        r="10"
        fill="white"
      />
    </>
  ),
});

CircleIcon.displayName = 'CircleIcon';

export interface AppRadioButtonsProps extends FieldValues {
  title: string;
  data: { label: string; value: string }[];
  error?: FieldError;
  containerClassName?: string;
  manualMode?: boolean;
  onValidate?: any;
}

const AppRadioButtons: FC<AppRadioButtonsProps> = ({
  title,
  data,
  error,
  containerClassName,
  manualMode,
  onValidate,
  ...props
}) => {
  return (
    <FormControl
      isInvalid={Boolean(error)}
      className={`bg-grey-card p-5 py-7 ${containerClassName}`}
    >
      <FormControlLabel>
        <FormControlLabelText
          className={`font-normal text-sm ${error ? "text-error-700" : "text-gold-ultralight"}`}
        >
          {title}
        </FormControlLabelText>
      </FormControlLabel>
      <RadioGroup {...props}>
        <HStack space="sm">
          {data.map((el, index) => (
            <Radio key={index} value={el.value}>
              <RadioIndicator>
                <RadioIcon as={CircleIcon} />
              </RadioIndicator>
              <RadioLabel>{el.label}</RadioLabel>
            </Radio>
          ))}
          {manualMode && props.isDirty !== undefined && (
            <Pressable className="ml-auto h-[10px]" onPress={onValidate}>
              {props.isDirty === false ? (
                <Pencil />
              ) : (
                <Check stroke="#89E789" />
              )}
            </Pressable>
          )}
        </HStack>
      </RadioGroup>
      <View
        className={`w-full h-[1px] mt-2 ${error ? "bg-error-700" : "bg-grey-600"}`}
      />
      <FormControlError>
        {error && (
          <>
            <FormControlErrorIcon as={AlertCircleIcon} />
            <FormControlErrorText>{error.message}</FormControlErrorText>
          </>
        )}
      </FormControlError>
    </FormControl>
  );
};

export default AppRadioButtons;
