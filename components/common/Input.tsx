import React, { <PERSON> } from "react";
import { TextInputProps } from "react-native";
import { FieldError, FieldValues } from "react-hook-form";
import { Input, InputField, InputSlot } from "../ui/input";
import {
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { AlertCircleIcon } from "../ui/icon";
import Pencil from "@/assets/images/icons/pencil.svg";
import Check from "@/assets/images/icons/check.svg";

export interface AppInputProps extends TextInputProps, FieldValues {
  title: string;
  placeholder: string;
  error?: FieldError;
  containerClassName?: string;
  manualMode?: boolean;
  onValidate?: any;
}

const AppInput: FC<AppInputProps> = ({
  title,
  placeholder,
  error,
  containerClassName,
  manualMode,
  onValidate,
  ...props
}) => {
  return (
    <FormControl
      isInvalid={Boolean(error)}
      className={`bg-grey-card p-5 py-7 ${containerClassName}`}
    >
      <FormControlLabel>
        <FormControlLabelText
          className={`font-normal text-sm ${error ? "text-error-700" : "text-gold-ultralight"}`}
        >
          {title}
        </FormControlLabelText>
      </FormControlLabel>
      <Input variant="underlined" size="md" className="text-gold-ultralight">
        <InputField
          type="text"
          placeholder={placeholder}
          autoCapitalize="none"
          {...props}
        />
        {manualMode && props.isDirty !== undefined && (
          <InputSlot onPress={onValidate}>
            {props.isDirty === false ? <Pencil /> : <Check stroke="#89E789" />}
          </InputSlot>
        )}
      </Input>
      <FormControlError>
        {error && (
          <>
            <FormControlErrorIcon as={AlertCircleIcon} />
            <FormControlErrorText>{error.message}</FormControlErrorText>
          </>
        )}
      </FormControlError>
    </FormControl>
  );
};

export default AppInput;
