import React from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ChevronRight from "@/assets/images/icons/chevron_right.svg";
import { SUPPORTED_CURRENCIES } from "@/constants/currencies";
import { useCurrency } from "@/providers/currency/currency.provider";
import { useI18n } from "@/providers/i18n/i18n.provider";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  SelectScrollView,
} from "../ui/select";
import { VStack } from "../ui/vstack";

export default function CurrencySelector() {
  const { currency, setCurrency } = useCurrency();
  const { t } = useI18n();
  const insets = useSafeAreaInsets();

  const handleCurrencyChange = async (currencyCode: string) => {
    if (currencyCode !== currency) {
      await setCurrency(currencyCode);
    }
  };

  const currentCurrency = SUPPORTED_CURRENCIES.find(
    (curr) => curr.code === currency
  );

  return (
    <VStack className="w-full">
      <Select
        className="rounded-none"
        onValueChange={handleCurrencyChange}
        defaultValue={currency}
      >
        <SelectTrigger
          variant="outline"
          size="md"
          className="justify-between pl-2 pr-5 bg-grey-card rounded-none h-[60px] border-0"
        >
          <SelectInput
            placeholder={t("profile.currency")}
            value={
              currentCurrency
                ? `${currentCurrency.name} (${currentCurrency.symbol})`
                : ""
            }
            size="md"
            className="rounded-none text-gold-ultralight"
          />
          <ChevronRight
            stroke="#747474"
            style={{ transform: [{ rotate: "90deg" }] }}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent
            style={{
              paddingBottom: insets.bottom,
              paddingTop: insets.top,
              maxHeight: "100%",
            }}
          >
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView contentContainerStyle={{ paddingBottom: 8 }}>
              {SUPPORTED_CURRENCIES.map((curr) => (
                <SelectItem
                  key={curr.code}
                  label={`${curr.name} (${curr.symbol})`}
                  value={curr.code}
                  className="h-[50px] rounded-none hover:bg-transparent active:bg-transparent pressed:bg-transparent"
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
}
