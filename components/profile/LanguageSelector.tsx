import React, { useEffect } from "react";
import { View, Text } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ChevronRight from "@/assets/images/icons/chevron_right.svg";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";
import { useI18n } from "@/providers/i18n/i18n.provider";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  SelectScrollView,
} from "../ui/select";
import { VStack } from "../ui/vstack";
import { SectionTitle } from "../search/SectionTitle";

export default function LanguageSelector() {
  const { locale, setLocale, t } = useI18n();
  const insets = useSafeAreaInsets();

  const handleLanguageChange = async (languageCode: string) => {
    if (languageCode !== locale) {
      await setLocale(languageCode);
    }
  };

  const currentLanguage = SUPPORTED_LANGUAGES.find(
    (lang) => lang.code === locale
  );

  return (
    <VStack className="w-full">
      {/* <SectionTitle title={t('profile.language')} /> */}
      <Select
        className="rounded-none"
        onValueChange={handleLanguageChange}
        defaultValue={locale}
      >
        <SelectTrigger
          variant="outline"
          size="md"
          className="justify-between pl-2 pr-5 bg-grey-card rounded-none h-[60px] border-0"
        >
          <SelectInput
            placeholder={t("profile.language")}
            value={currentLanguage ? `${currentLanguage.nativeName}` : ""}
            size="md"
            className="rounded-none text-gold-ultralight"
          />
          <ChevronRight
            stroke="#747474"
            style={{ transform: [{ rotate: "90deg" }] }}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent
            style={{
              paddingBottom: insets.bottom,
              paddingTop: insets.top,
              maxHeight: "100%",
            }}
          >
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView contentContainerStyle={{ paddingBottom: 8 }}>
              {SUPPORTED_LANGUAGES.map((language) => (
                <SelectItem
                  key={language.code}
                  label={`${language.nativeName}`}
                  value={language.code}
                  className="h-[50px] rounded-none hover:bg-transparent active:bg-transparent pressed:bg-transparent"
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
}
