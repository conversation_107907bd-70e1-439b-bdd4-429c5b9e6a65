import React from "react";
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { View } from "react-native";
import { Text } from "@/components/ui/text";
import GradientButton from "@/components/common/GradientButton";
import { router } from "expo-router";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { HeaderLogo } from "../layout/HeaderLogo";

export default function Unauthenticated() {
  const { t } = useI18n();
  return (
    <SafeAreaView className="flex-1">
      <HeaderLogo />
      <View className="flex-1 justify-center items-center px-4">
        <Text className="text-2xl font-bold text-center text-white mb-4">
          {t('profile.unauthenticated')}
        </Text>
        <Text className="text-gold-ultralight text-center text-lg mb-4">
          {t('profile.unauthenticated_infos')}
        </Text>
        <GradientButton onPress={() => router.push("/onboarding")}>
          {t('auth.back_to_login')}
        </GradientButton>
      </View>
    </SafeAreaView>
  );
}
