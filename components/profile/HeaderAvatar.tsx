import React from "react";
import { Dimensions, View } from "react-native";
import { VStack } from "../ui/vstack";

import StarWarsBackground from "@/assets/images/headers/star-wars.svg";
import { Avatar, AvatarFallbackText, AvatarImage } from "../ui/avatar";
import { components } from "@/@types/api.types";
import { Text } from "@/components/ui/text";
import { useI18n } from "@/providers/i18n/i18n.provider";

export default function HeaderAvatar({
  user,
}: {
  user: components["schemas"]["User"];
}) {
  const { t } = useI18n();
  const screenWidth = Dimensions.get("window").width;

  return (
    <VStack className="relative w-full items-center justify-center">
      <View className="absolute top-0 left-0 -z-0">
        <StarWarsBackground width={screenWidth} height={128} />
      </View>

      <View className="mt-[100px] h-[120px] bg-black w-full border-t border-grey-900">
        <VStack space="xs" className="-top-12 flex justify-center items-center">
          <Avatar size="xl" className="bg-black border border-gold-200">
            <AvatarFallbackText className="text-gold-ultralight">
              {user.username}
            </AvatarFallbackText>
            <AvatarImage source={require("@/assets/images/logo.png")} />
          </Avatar>
          <Text className="mt-2 text-gold-ultralight">
            {t("profile.hello")},
          </Text>
          <Text className="text-gold-ultralight text-xl font-body-bold">
            @{user.username}
          </Text>
        </VStack>
      </View>
    </VStack>
  );
}
