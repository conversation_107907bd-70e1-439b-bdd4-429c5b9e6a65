import { Center } from "../ui/center";
import { <PERSON><PERSON>, ButtonText } from "../ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../ui/modal";
import { Heading } from "../ui/heading";
import { Icon, CloseIcon } from "../ui/icon";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { VStack } from "../ui/vstack";
import ControlledPasswordInput from "../form/ControlledPasswordInput";
import { toast } from "sonner-native";
import axiosInstance from "@/api/axios";
import { useI18n } from "@/providers/i18n/i18n.provider";
import React from "react";

interface PasswordFormDialogProps {
  open: boolean;
  onClose: VoidFunction;
}

export default function PasswordFormDialog({
  open,
  onClose,
}: PasswordFormDialogProps) {
  const { t } = useI18n();
  
  const passwordSchema = z
    .object({
      old_password: z.string().min(1, t("profile.errors.old_password_required")),
      password: z.string().min(1, t("profile.errors.new_password_required")),
      password_confirmation: z
        .string()
        .min(1, t("profile.errors.password_confirmation_required")),
    })
    .refine((data) => data.password === data.password_confirmation, {
      message: t("profile.errors.password_mismatch"),
      path: ["password_confirmation"],
    });

  type PasswordSchemaType = z.infer<typeof passwordSchema>;

  const { handleSubmit, control } = useForm<PasswordSchemaType>({
    defaultValues: {
      old_password: "",
      password: "",
      password_confirmation: "",
    },
    mode: "onSubmit",
    resolver: zodResolver(passwordSchema),
  });

  const onSubmit = async (form: PasswordSchemaType) => {
    try {
      await axiosInstance.put("/profile", form);
      toast.success(t("profile.password_updated"));
      onClose();
    } catch (e) {
      console.error(e);
      toast.error(t("profile.errors.password_update_error"));
    }
  };

  return (
    <Center>
      <Modal isOpen={open} onClose={onClose} size="lg">
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <Heading
              size="md"
              className="text-typography-950 text-gold-ultralight"
            >
              {t("profile.change_password")}
            </Heading>
            <ModalCloseButton onPress={onClose}>
              <Icon
                as={CloseIcon}
                size="md"
                className="stroke-background-400 group-[:hover]/modal-close-button:stroke-background-700 group-[:active]/modal-close-button:stroke-background-900 group-[:focus-visible]/modal-close-button:stroke-background-900"
              />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <VStack className="py-3">
              <ControlledPasswordInput
                name="old_password"
                control={control}
                title={t("profile.current_password")}
                placeholder={t("profile.current_password")}
              />
              <ControlledPasswordInput
                name="password"
                control={control}
                title={t("profile.new_password")}
                placeholder={t("profile.new_password")}
              />
              <ControlledPasswordInput
                name="password_confirmation"
                control={control}
                title={t("profile.password_confirmation")}
                placeholder={t("profile.password_confirmation")}
              />
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" action="secondary" onPress={onClose}>
              <ButtonText className="text-gold-ultralight">
                {t("profile.cancel")}
              </ButtonText>
            </Button>
            <Button onPress={handleSubmit(onSubmit)}>
              <ButtonText>{t("profile.confirm")}</ButtonText>
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Center>
  );
}
