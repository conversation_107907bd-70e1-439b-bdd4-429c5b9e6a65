import React from "react";
import { Linking } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { TouchableOpacity } from "react-native";
import { HStack } from "@/components/ui/hstack";
import ChevronRight from "@/assets/images/icons/chevron_right.svg";
import { Text } from "@/components/ui/text";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";

export default function LegalList() {
  const constants = useTranslatedConstants();
  const { LEGAL_DATA } = constants;

  const openURL = (url: string) => {
    Linking.openURL(url).catch((err) =>
      console.error("Couldn't open URL:", err),
    );
  };

  return (
    <VStack className="bg-grey-card p-5">
      {LEGAL_DATA.map((legal, index) => (
        <TouchableOpacity
          key={index}
          className="border-b border-grey-600 py-4"
          onPress={() => openURL(legal.url)}
        >
          <HStack className="justify-between">
            <Text className="font-normal text-base text-gold-ultralight">
              {legal.label}
            </Text>
            <ChevronRight stroke="#747474" />
          </HStack>
        </TouchableOpacity>
      ))}
    </VStack>
  );
}
