import { components } from "@/@types/api.types";
import { Card } from "@/components/ui/card";
import { Image } from "@/components/ui/image";
import { backAssetsUrl } from "@/utils/domain";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { VStack } from "../ui/vstack";
import { ViewStyle, View } from "react-native";
import { useCallback, useMemo, useState } from "react";
import { LinearGradient } from "@/components/ui/linear-gradient";

type Props = {
  brand: components["schemas"]["Brand"];
  className?: string;
  style?: ViewStyle;
  showPerfumesCount?: boolean;
  hideMarqueLabel?: boolean;
  useGoldGradient?: boolean;
};

export function BrandCard({ brand, className, style, showPerfumesCount = true, hideMarqueLabel = false, useGoldGradient = false }: Props) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const brandInitial = useMemo(() => {
    const name = brand.display_name || brand.name || "?";
    // Handle dot prefix for display
    if (name.startsWith('.') && name.length > 1) {
      return name[1].toUpperCase();
    }
    return name[0].toUpperCase();
  }, [brand.display_name, brand.name]);

  // Use the perfumesCount from the brand model (if available)
  const perfumesCount = (brand as any).perfumesCount || 0;

  const cardContent = (
    <>
      <View className="flex-none">
        <View className="w-16 h-16">
          {brand.logo?.url && !imageError ? (
            <>
              {!imageLoaded && (
                <View className="w-16 h-16 bg-gray-200 absolute rounded-md" />
              )}
              <Image
                source={{ uri: backAssetsUrl(brand.logo.url) }}
                alt={brand.name}
                className="w-16 h-16 rounded-md"
                onError={handleImageError}
                onLoad={handleImageLoad}
              />
            </>
          ) : (
            <View className="w-16 h-16 bg-white items-center justify-center rounded-md">
              <Text className="text-black text-2xl font-bold">{brandInitial}</Text>
            </View>
          )}
        </View>
      </View>

      <VStack className="flex-1 ml-4">
        <View className="flex-row items-center justify-between">
          <View className={`flex-1 ${hideMarqueLabel ? 'items-center' : ''}`}>
            <Heading
              className={`${useGoldGradient ? 'text-black' : 'text-gold-ultralight'} text-xl font-semibold font-sans`}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {brand.display_name || brand.name}
            </Heading>
            {!hideMarqueLabel && (
              <Text className={`text-xs ${useGoldGradient ? 'bg-black text-gold-ultralight' : 'bg-gold-ultralight text-grey-card'} px-2 py-1 rounded-full mt-1 self-start`}>
                Marque
              </Text>
            )}
          </View>

          {showPerfumesCount && perfumesCount > 0 && (
            <View className="ml-2 mr-2">
              <View className={`w-12 h-12 ${hideMarqueLabel ? 'bg-transparent' : 'bg-white'} items-center justify-center rounded-md`}>
                <Text
                  className="text-black text-xl font-bold font-sans"
                >
                  {perfumesCount}
                </Text>
                <Text
                  className="text-black font-bold"
                  style={{
                    fontFamily: "MF2005"
                  }}
                >
                  sru.
                </Text>
              </View>
            </View>
          )}
        </View>
      </VStack>
    </>
  );

  if (useGoldGradient) {
    return (
      <LinearGradient
        colors={["#F1CA90", "#BF9E57"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className={`flex flex-row items-center justify-between p-4 rounded-none ${className || ""}`}
        style={style}
      >
        {cardContent}
      </LinearGradient>
    );
  }

  return (
    <Card
      className={`bg-grey-card flex flex-row items-center justify-between p-4 rounded-none ${className || ""}`}
      size="sm"
      style={style}
    >
      {cardContent}
    </Card>
  );
}

export default BrandCard;
