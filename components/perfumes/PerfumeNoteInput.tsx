import React, { useCallback } from "react";
import { TextInput } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { PerfumeNote } from "@/@types/api.types";

type Props = {
  perfumeId: number;
  className?: string;
};

export function PerfumeNoteInput({ perfumeId, className }: Props) {
  const { t } = useI18n();
  const dispatch = useDispatch();

  // @ts-ignore
  const user = useSelector((state) => state.auth.user);
  // @ts-ignore
  const existingNote = useSelector((state: any) =>
    state.favorites.notes.find((note: PerfumeNote) => note.perfumeId === perfumeId),
  );

  if (!user) {
    return null;
  }

  const handleTextChange = useCallback((text: string) => {
    if (text.trim()) {
      if (existingNote) {
        dispatch.favorites.editNote({
          id: existingNote.id,
          content: text.trim(),
        });
      } else {
        dispatch.favorites.saveNote({
          perfumeId,
          content: text.trim(),
          userId: user.id,
        });
      }
    } else if (existingNote) {
      dispatch.favorites.deleteNote(existingNote.id);
    }
  }, [existingNote, dispatch, perfumeId, user.id]);

  return (
    <TextInput
      defaultValue={existingNote?.content || ""}
      onChangeText={handleTextChange}
      placeholder={t("personalNotes.placeholder")}
      placeholderTextColor="#666666"
      multiline
      textAlignVertical="top"
      className={`bg-grey-card rounded-none p-4 ${className || ""}`}
      style={{
        color: "#CCCCCC",
        fontSize: 14,
        lineHeight: 20,
        minHeight: 56, // 1 line + 16px padding each side
        maxHeight: 160, // ~8 lines max
      }}
    />
  );
}