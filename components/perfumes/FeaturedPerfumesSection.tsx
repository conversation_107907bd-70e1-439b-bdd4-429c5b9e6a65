import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { SectionTitle } from "@/components/layout/SectionTitle";
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { Dimensions, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { BasicPerfumeCard } from "../perfumes/BasicPerfumeCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

export function FeaturedPerfumesSection() {
  const router = useRouter();
  const { t } = useI18n();
  const { data: perfumes } = useSWR<{
    data: components["schemas"]["Perfume"][];
  }>("/perfumes?is_featured=true", { keepPreviousData: false });

  const screenWidth = Dimensions.get("window").width;

  const openPerfume = (perfumeId: number) => {
    router.push(`/(app)/(tabs)/(home)/search/perfume/${perfumeId}`);
  };

  const featuredPerfumes = perfumes?.data?.slice(0, 4) || [];
  const firstRow = featuredPerfumes.slice(0, 2);
  const secondRow = featuredPerfumes.slice(2, 4);

  return (
    <VStack space="4xl" className="mt-6">
      <SectionTitle title={t("perfume.most_wanted_perfumes")} />

      <VStack space="md" className="mb-8 px-4">
        {firstRow.length > 0 && (
          <HStack space="md" className="justify-between">
            {firstRow.map((item, index) => (
              <TouchableOpacity
                key={index}
                activeOpacity={0.6}
                onPress={() => openPerfume(item.id!)}
                style={{ flex: 1 }}
              >
                <BasicPerfumeCard
                  perfume={item}
                  style={{
                    width: screenWidth / 2 - 24,
                  }}
                />
              </TouchableOpacity>
            ))}
          </HStack>
        )}
        
        {secondRow.length > 0 && (
          <HStack space="md" className="justify-between">
            {secondRow.map((item, index) => (
              <TouchableOpacity
                key={index + 2}
                activeOpacity={0.6}
                onPress={() => openPerfume(item.id!)}
                style={{ flex: 1 }}
              >
                <BasicPerfumeCard
                  perfume={item}
                  style={{
                    width: screenWidth / 2 - 24,
                  }}
                />
              </TouchableOpacity>
            ))}
          </HStack>
        )}
      </VStack>
    </VStack>
  );
}
