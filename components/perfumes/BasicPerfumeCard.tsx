import { components } from "@/@types/api.types";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Image } from "@/components/ui/image";
import { backAssetsUrl } from "@/utils/domain";
import { Heading } from "@/components/ui/heading";

type Props = {
  perfume: components["schemas"]["Perfume"];
  style?: any;
};

export function BasicPerfumeCard({ perfume, style }: Props) {
  return (
    <Card
      className="bg-grey-card items-center justify-center rounded-none py-6"
      style={style}
    >
      {perfume.picture?.url && (
        <Image
          size="lg"
          source={{ uri: backAssetsUrl(perfume.picture?.url) }}
          alt={perfume.name}
        />
      )}
      <Heading
        numberOfLines={1}
        ellipsizeMode="tail"
        className="text-gold-ultralight mt-8 text-xl font-heading-semibold"
      >
        {perfume.display_name || perfume.name}
      </Heading>
      <Text 
        numberOfLines={1}
        ellipsizeMode="tail"
        className="mt-1 text-grey-200 text-sm"
      >
        {perfume.brand?.display_name?.toUpperCase() ||
          perfume.brand?.name?.toUpperCase()}
      </Text>
    </Card>
  );
}
