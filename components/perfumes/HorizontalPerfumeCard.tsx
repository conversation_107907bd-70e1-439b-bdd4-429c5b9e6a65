import { components } from "@/@types/api.types";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Image } from "@/components/ui/image";
import { backAssetsUrl } from "@/utils/domain";
import { Heading } from "@/components/ui/heading";
import { TouchableOpacity } from "react-native";
import { PerfumeChips } from "./PerfumeChips";
import StarFull from "@/assets/images/icons/star-full.svg";
import StarOutline from "@/assets/images/icons/star-outline.svg";
import { useState } from "react";
import { VStack } from "../ui/vstack";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";

type Props = {
  perfume: components["schemas"]["Perfume"];
  handleMarkAsFavorites?: (perfumeId: number) => void;
  percentage?: number;
  className?: string;
};

export function HorizontalPerfumeCard({
  perfume,
  handleMarkAsFavorites,
  percentage,
  ...props
}: Props) {
  const [isFavorite, setIsFavorite] = useState(true);
  const constants = useTranslatedConstants();
  const { PERFUME_TYPES, getGenderLabel } = constants;

  const toggleFavorite = () => {
    const newState = !isFavorite;
    setIsFavorite(newState);
    if (handleMarkAsFavorites) {
      handleMarkAsFavorites(perfume.id!);
    }
  };

  return (
    <Card
      className={`bg-grey-card flex flex-row items-center justify-between p-4 rounded-none ${props.className || ""}`}
      size="sm"
    >
      {perfume.picture?.url && (
        <Image
          source={{ uri: backAssetsUrl(perfume.picture?.url) }}
          alt={perfume.name}
          className="w-16 h-16 rounded-md"
        />
      )}

      <VStack className="flex-1 ml-4">
        <Heading
          className="text-gold-ultralight text-xl font-semibold font-sans"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {perfume.display_name || perfume.name}
        </Heading>
        <Text className="mt-1 mb-1 text-grey-200 font-serif text-[12px]">
          {perfume.brand?.display_name?.toUpperCase() ||
            perfume.brand?.name?.toUpperCase()}
        </Text>

        <VStack className="flex-row gap-2 mt-2">
          {perfume.year && <PerfumeChips content={perfume.year} />}
          {perfume.type && (
            <PerfumeChips
              content={PERFUME_TYPES[perfume.type].toLocaleUpperCase()}
            />
          )}
          {perfume.gender && (
            <PerfumeChips
              content={getGenderLabel(perfume.gender).toLocaleUpperCase()}
            />
          )}
        </VStack>
      </VStack>

      {handleMarkAsFavorites && (
        <VStack className="top-[-30px] relative">
          <TouchableOpacity onPress={toggleFavorite}>
            {isFavorite ? <StarFull /> : <StarOutline />}
          </TouchableOpacity>
        </VStack>
      )}

      {percentage && (
        <VStack className="relative -top-7 px-1.5 border border-[#A2A2A2] rounded-full flex items-center justify-center">
          <Text
            style={{
              fontSize: 12,
              fontWeight: "500",
              color: "#A2A2A2",
            }}
          >
            {percentage}%
          </Text>
        </VStack>
      )}
    </Card>
  );
}
