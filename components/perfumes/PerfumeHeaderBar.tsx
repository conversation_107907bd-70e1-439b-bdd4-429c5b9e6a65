import { HStack } from "@/components/ui/hstack";
import { TouchableOpacity } from "react-native";
import { Alert } from "@/components/ui/custom-alert";
import { Path, Svg } from "react-native-svg";
import { useRouter } from "expo-router";
import { store } from "@/store";
import { connect, useSelector } from "react-redux";
import React, { useMemo } from "react";
import StarFilled from "@/assets/images/icons/star-full.svg";
import StarOutline from "@/assets/images/icons/star-outline.svg";
import { useI18n } from "@/providers/i18n/i18n.provider";

type Props = {
  perfumeId: number;
  perfumesFav: number[];
  addFavorite: (id: number) => void;
  removeFavorite: (id: number) => void;
};

function PerfumeHeaderBar({
  perfumeId,
  perfumesFav,
  removeFavorite,
  addFavorite,
}: Props) {
  const router = useRouter();
  const { t } = useI18n();
  // @ts-ignore
  const auth = useSelector((state) => state.auth);

  const isFav = useMemo(
    () => perfumesFav.includes(perfumeId),
    [perfumesFav, perfumeId],
  );

  const toggleFavorite = () => {
    if (!auth.isAuthenticated) {
      Alert.alert(t("common.alert"), t("favorites.errors.not_authenticated"));
    } else {
      if (isFav) {
        removeFavorite(perfumeId);
      } else {
        addFavorite(perfumeId);
      }
    }
  };

  return (
    <HStack className="justify-between items-center">
      <TouchableOpacity activeOpacity={0.6} onPress={() => router.back()}>
        <Svg width="20" height="12" viewBox="0 0 20 12" fill="none">
          <Path
            d="M1 6L6 11M1 6L6 1M1 6H19"
            stroke="#F2ECE0"
            stroke-linecap="square"
          />
        </Svg>
      </TouchableOpacity>
      <TouchableOpacity activeOpacity={0.6} onPress={toggleFavorite}>
        {isFav ? <StarFilled /> : <StarOutline />}
      </TouchableOpacity>
    </HStack>
  );
}

const mapState = store.select((models) => ({
  perfumesFav: models.favorites.ids,
}));
const mapDispatch = (dispatch: any) => ({
  addFavorite: dispatch.favorites.addFavorite,
  removeFavorite: dispatch.favorites.removeFavorite,
});

export default connect(mapState, mapDispatch)(PerfumeHeaderBar);
