import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { SectionTitle } from "@/components/layout/SectionTitle";
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { Dimensions, TouchableOpacity } from "react-native";
import { BrandCard } from "../perfumes/BrandCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

interface FeaturedBrandsSectionProps {
  onBrandSelect?: (brand: components["schemas"]["Brand"]) => void;
}

export function FeaturedBrandsSection({ onBrandSelect }: FeaturedBrandsSectionProps) {
  const { t } = useI18n();
  const { data: brands } = useSWR<{
    data: components["schemas"]["Brand"][];
  }>("/brands?is_featured=true", { keepPreviousData: false });

  const screenWidth = Dimensions.get("window").width;

  const handleBrandClick = (brand: components["schemas"]["Brand"]) => {
    if (onBrandSelect) {
      onBrandSelect(brand);
    }
  };

  const featuredBrands = brands?.data?.slice(0, 4) || [];
  const firstRow = featuredBrands.slice(0, 2);
  const secondRow = featuredBrands.slice(2, 4);

  return (
    <VStack space="4xl" className="mt-6">
      <SectionTitle title={t("brand.most_wanted_brands")} />

      <VStack space="md" className="mb-8 px-4">
        {firstRow.length > 0 && (
          <HStack space="md" className="justify-between">
            {firstRow.map((item, index) => (
              <TouchableOpacity
                key={index}
                activeOpacity={0.6}
                onPress={() => handleBrandClick(item)}
                style={{ flex: 1 }}
              >
                <BrandCard
                  brand={item}
                  style={{
                    width: screenWidth / 2 - 24,
                  }}
                  showPerfumesCount={false}
                />
              </TouchableOpacity>
            ))}
          </HStack>
        )}
        
        {secondRow.length > 0 && (
          <HStack space="md" className="justify-between">
            {secondRow.map((item, index) => (
              <TouchableOpacity
                key={index + 2}
                activeOpacity={0.6}
                onPress={() => handleBrandClick(item)}
                style={{ flex: 1 }}
              >
                <BrandCard
                  brand={item}
                  style={{
                    width: screenWidth / 2 - 24,
                  }}
                  showPerfumesCount={false}
                />
              </TouchableOpacity>
            ))}
          </HStack>
        )}
      </VStack>
    </VStack>
  );
}