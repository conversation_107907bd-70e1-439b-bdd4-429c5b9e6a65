import { components } from "@/@types/api.types";
import { VStack } from "@/components/ui/vstack";
import { Divider } from "@/components/ui/divider";
import StarIcon from "@/assets/images/icons/star-point.svg";
import { Heading } from "@/components/ui/heading";
import { HStack } from "@/components/ui/hstack";
import { useMemo } from "react";
import { Text } from "@/components/ui/text";
import { TouchableOpacity, View } from "react-native";
import ChevronDown from "@/assets/images/icons/chevron_down.svg";
import { LinearGradient } from "expo-linear-gradient";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useCurrency } from "@/providers/currency/currency.provider";
import { ShoppingBagIcon, LoaderIcon } from "lucide-react-native";
import { useLinkHandler } from "@/hooks/useLinkHandler";

type VendorPrice = {
  vendor: string;
  price: number;
  size: number;
  link: string;
  pricePerMl: number;
  isMotherHouse: boolean;
};

type Props = {
  perfume: components["schemas"]["Perfume"];
  selectedSize: number;
  showCompactView?: boolean;
  onShowAllVendors?: () => void;
  showAllVendors?: boolean;
};

export function PerfumePriceComparisonSection({
  perfume,
  selectedSize,
  showCompactView = false,
  onShowAllVendors,
  showAllVendors = false,
}: Props) {
  const { t } = useI18n();
  const { convertPrice, formatCurrency } = useCurrency();
  const { openLink, isLinkActive } = useLinkHandler({
    loadingDelay: 100,
    resetDelay: 100,
  });

  // Get vendor prices for the selected size
  const vendorPrices = useMemo(() => {
    if (!perfume.variants || !selectedSize) {
      return [];
    }

    // Filter variants by selected size
    const sizeVariants = perfume.variants.filter(
      (variant) =>
        variant.size === selectedSize && variant.price && variant.vendor
    );

    // Map to VendorPrice objects with price per ml calculation
    const prices: VendorPrice[] = sizeVariants.map((variant) => {
      const isMotherHouse = isVendorMotherHouse(
        variant.vendor || "",
        perfume.brand?.name || ""
      );
      return {
        vendor: variant.vendor || "",
        price: variant.price ? Number(variant.price.toFixed(2)) : 0,
        size: variant.size || 0,
        link: variant.link || "",
        pricePerMl: variant.price
          ? Number((variant.price / (variant.size || 1)).toFixed(2))
          : 0,
        isMotherHouse,
      };
    });

    // Sort by price (lowest first)
    return prices.sort((a, b) => a.price - b.price);
  }, [perfume.variants, selectedSize, perfume.brand?.name]);

  // Get the cheapest vendor
  const cheapestVendor = useMemo(() => {
    if (vendorPrices.length === 0) {
      return null;
    }
    return vendorPrices[0];
  }, [vendorPrices]);

  // Function to check if vendor is a mother house
  function isVendorMotherHouse(vendor: string, brandName: string): boolean {
    // Normalize strings for comparison
    const normalizedVendor = vendor.toLowerCase().trim();
    const normalizedBrand = brandName.toLowerCase().trim();

    // Check if vendor name contains brand name or vice versa
    return (
      normalizedVendor.includes(normalizedBrand) ||
      normalizedBrand.includes(normalizedVendor)
    );
  }

  // If no variants with prices, don't show the section
  if (!vendorPrices.length) {
    return null;
  }

  // Compact view for integration with PerfumeInfoSection
  if (showCompactView) {
    return (
      <VStack space="sm">
        {/* Vendor list - shows top vendors by default or all when expanded */}
        <VStack space="sm" className="mb-0.5">
          {vendorPrices
            .slice(
              1,
              showAllVendors
                ? vendorPrices.length
                : Math.min(3, vendorPrices.length)
            )
            .map((vendor, index) => (
              <TouchableOpacity
                key={`${vendor.vendor}-${index}`}
                onPress={() =>
                  vendor.link ? openLink(vendor.link, vendor.vendor) : null
                }
                activeOpacity={vendor.link ? 0.6 : 1}
                className="px-0.5"
                disabled={isLinkActive(vendor.vendor)}
              >
                <HStack className="justify-between items-center">
                  <VStack className="max-w-[65%]">
                    <HStack space="xs" className="items-center">
                      <Text
                        className="text-grey-400 font-body-semibold text-sm"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {vendor.vendor}
                      </Text>
                      {vendor.isMotherHouse && (
                        <View className="bg-gold-ultralight/10 px-1.5 py-0.5">
                          <Text className="text-gold-ultralight italic text-[10px]">
                            {t("perfume.mother_house")}
                          </Text>
                        </View>
                      )}
                    </HStack>
                    <HStack space="xs" className="items-center mt-0.5">
                      <Text className="text-grey-400 text-xs">
                        {formatCurrency(convertPrice(vendor.pricePerMl))}
                      </Text>
                      <Text className="text-grey-500 text-[10px]">/ ml</Text>
                    </HStack>
                  </VStack>
                  <HStack space="xs" className="items-center">
                    <Text className="font-body-semibold text-grey-300">
                      {formatCurrency(convertPrice(vendor.price))}
                    </Text>
                    {vendor.link && (
                      <View className="mx-1.5 h-5 w-[0.75px] bg-grey-600/50" />
                    )}
                    {vendor.link &&
                      (isLinkActive(vendor.vendor) ? (
                        <LoaderIcon
                          width={14}
                          height={14}
                          color="#F2ECE0"
                          strokeWidth={2}
                        />
                      ) : (
                        <ShoppingBagIcon
                          width={14}
                          height={14}
                          color="#F2ECE0"
                          strokeWidth={2}
                        />
                      ))}
                  </HStack>
                </HStack>
                {index <
                  vendorPrices.slice(
                    1,
                    showAllVendors
                      ? vendorPrices.length
                      : Math.min(3, vendorPrices.length)
                  ).length -
                    1 && (
                  <Divider className="bg-grey-600/40 mt-3 mb-1 h-[0.5px] w-full" />
                )}
              </TouchableOpacity>
            ))}
        </VStack>

        {/* Divider */}
        <Divider className="bg-grey-600/40 h-[0.5px] w-full" />

        {/* Section title */}
        <HStack className="justify-center items-center">
          {vendorPrices.length > 3 && (
            <TouchableOpacity
              onPress={onShowAllVendors}
              activeOpacity={0.6}
              className="py-0.5"
            >
              <HStack space="xs" className="items-center">
                <Text className="text-gold-ultralight text-xs font-body-medium">
                  {showAllVendors
                    ? t("perfume.show_less")
                    : t("perfume.show_more")}
                </Text>
                <ChevronDown
                  width={10}
                  height={10}
                  color="#F2ECE0"
                  style={
                    showAllVendors
                      ? { transform: [{ rotate: "180deg" }] }
                      : undefined
                  }
                />
              </HStack>
            </TouchableOpacity>
          )}
        </HStack>
      </VStack>
    );
  }

  // Full standalone view
  return (
    <VStack space="2xl">
      <VStack>
        <Divider className="h-[0.5px] w-full" />
        <HStack space="xl" className="items-center justify-center my-3">
          <StarIcon color="#F2ECE0" />
          <Heading className="text-gold-ultralight text-[24px]">
            {t("perfume.price_comparison")}
          </Heading>
          <StarIcon color="#F2ECE0" />
        </HStack>
        <Divider className="h-[0.5px] w-full" />
      </VStack>

      {/* Default price (cheapest) */}
      {cheapestVendor && (
        <TouchableOpacity
          onPress={() =>
            cheapestVendor.link
              ? openLink(cheapestVendor.link, cheapestVendor.vendor)
              : null
          }
          activeOpacity={cheapestVendor.link ? 0.6 : 1}
          disabled={isLinkActive(cheapestVendor.vendor)}
        >
          <LinearGradient
            colors={["rgba(191, 158, 87, 0.42)", "rgba(191, 158, 87, 0.18)"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="border-[0.5px] border-gold-ultralight/40 shadow-sm"
          >
            <View className="p-4">
              <VStack space="md">
                <HStack className="justify-between">
                  <Text className="text-gold-ultralight font-body-bold text-lg">
                    {t("perfume.default_price")}
                  </Text>
                  <Text className="text-gold-ultralight font-body-bold text-xl">
                    {formatCurrency(convertPrice(cheapestVendor.price))}
                  </Text>
                </HStack>
                <HStack className="justify-between items-center">
                  <VStack className="max-w-[65%]">
                    <HStack space="xs" className="items-center">
                      <Text
                        className="text-grey-300 font-body-semibold"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {cheapestVendor.vendor}
                      </Text>
                      {cheapestVendor.isMotherHouse && (
                        <View className="bg-gold-ultralight/20 px-2 py-0.5">
                          <Text className="text-gold-ultralight italic text-xs">
                            {t("perfume.mother_house")}
                          </Text>
                        </View>
                      )}
                    </HStack>
                    <HStack space="xs" className="items-center mt-1">
                      <Text className="text-grey-400 text-xs">
                        {formatCurrency(
                          convertPrice(cheapestVendor.pricePerMl)
                        )}
                      </Text>
                      <Text className="text-grey-500 text-[10px]">/ ml</Text>
                    </HStack>
                  </VStack>
                  <HStack className="items-center">
                    <Text className="font-body-bold text-lg text-gold-ultralight">
                      {formatCurrency(convertPrice(cheapestVendor.price))}
                    </Text>
                    {cheapestVendor.link && (
                      <View className="mx-1.5 h-3.5 w-[0.5px] bg-gold-ultralight/30" />
                    )}
                    {cheapestVendor.link &&
                      (isLinkActive(cheapestVendor.vendor) ? (
                        <LoaderIcon
                          width={18}
                          height={18}
                          color="#F2ECE0"
                          strokeWidth={2.5}
                        />
                      ) : (
                        <ShoppingBagIcon
                          width={18}
                          height={18}
                          color="#F2ECE0"
                          strokeWidth={2.5}
                        />
                      ))}
                  </HStack>
                </HStack>
              </VStack>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      )}

      {/* Other vendors list */}
      <VStack space="md" className="mt-3">
        <HStack className="justify-between items-center">
          <Text className="text-grey-300 font-body-semibold text-base">
            {t("perfume.compare_prices")}
          </Text>
          {vendorPrices.length > 3 && (
            <TouchableOpacity
              onPress={onShowAllVendors}
              activeOpacity={0.6}
              className="py-0.5"
            >
              <HStack space="xs" className="items-center">
                <Text className="text-gold-ultralight text-sm font-body-medium">
                  {showAllVendors
                    ? t("perfume.show_less")
                    : t("perfume.show_more")}
                </Text>
                <ChevronDown
                  width={12}
                  height={12}
                  color="#F2ECE0"
                  style={
                    showAllVendors
                      ? { transform: [{ rotate: "180deg" }] }
                      : undefined
                  }
                />
              </HStack>
            </TouchableOpacity>
          )}
        </HStack>

        <Divider className="bg-grey-600/40 h-[0.5px] w-full" />

        {/* Vendor price cards */}
        <VStack space="md" className="mt-0.5">
          {vendorPrices
            .slice(1, showAllVendors ? vendorPrices.length : 3)
            .map((vendor, index) => (
              <TouchableOpacity
                key={`${vendor.vendor}-${index}`}
                onPress={() =>
                  vendor.link ? openLink(vendor.link, vendor.vendor) : null
                }
                activeOpacity={vendor.link ? 0.6 : 1}
                disabled={isLinkActive(vendor.vendor)}
                className="py-1.5 px-0.5"
              >
                <HStack className="justify-between items-center">
                  <VStack className="max-w-[65%]">
                    <HStack space="xs" className="items-center">
                      <Text
                        className="text-grey-400 font-body-semibold"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {vendor.vendor}
                      </Text>
                      {vendor.isMotherHouse && (
                        <View className="bg-gold-ultralight/20 px-2 py-0.5">
                          <Text className="text-gold-ultralight italic text-xs">
                            {t("perfume.mother_house")}
                          </Text>
                        </View>
                      )}
                    </HStack>
                    <HStack space="xs" className="items-center mt-1">
                      <Text className="text-grey-400 text-sm">
                        {formatCurrency(convertPrice(vendor.pricePerMl))}
                      </Text>
                      <Text className="text-grey-500 text-[10px]">/ ml</Text>
                    </HStack>
                  </VStack>
                  <HStack className="items-center">
                    <Text className="font-body-bold text-lg">
                      {formatCurrency(convertPrice(vendor.price))}
                    </Text>
                    {vendor.link && (
                      <View className="mx-1.5 h-3 w-[0.5px] bg-grey-600/30" />
                    )}
                    {vendor.link &&
                      (isLinkActive(vendor.vendor) ? (
                        <LoaderIcon
                          width={16}
                          height={16}
                          color="#F2ECE0"
                          strokeWidth={2.5}
                        />
                      ) : (
                        <ShoppingBagIcon
                          width={16}
                          height={16}
                          color="#F2ECE0"
                          strokeWidth={2.5}
                        />
                      ))}
                  </HStack>
                </HStack>
                {index <
                  vendorPrices.slice(
                    1,
                    showAllVendors ? vendorPrices.length : 3
                  ).length -
                    1 && (
                  <Divider className="bg-grey-600/20 mt-2 mb-0.5 h-[0.5px] w-full" />
                )}
              </TouchableOpacity>
            ))}
        </VStack>
      </VStack>
    </VStack>
  );
}
