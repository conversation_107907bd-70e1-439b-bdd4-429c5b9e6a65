import { components } from "@/@types/api.types";
import { VStack } from "@/components/ui/vstack";
import StarFlash from "@/assets/images/home/<USER>";
import { Heading } from "@/components/ui/heading";
import {
  ClipPath,
  Defs,
  G,
  Line,
  Mask,
  Path,
  Rect,
  Svg,
} from "react-native-svg";
import { Text } from "@/components/ui/text";
import { StyleSheet } from "react-native";

type Props = {
  perfume: components["schemas"]["Perfume"];
};

export function PerfumeHistorySection({ perfume }: Props) {
  return (
    <VStack
      style={styles.church}
      className="items-center pb-10 bg-grey-card px-2"
      space="2xl"
    >
      <StarFlash
        style={{
          marginTop: 30,
        }}
      />
      <VStack className="px-4">
        <Heading className="font-heading-bold text-[24px] text-center text-gold-ultralight">
          Histoire
        </Heading>
      </VStack>
      <Svg width="173" height="16" viewBox="0 0 173 16" fill="none">
        <Line
          x1="0.5"
          y1="7.75"
          x2="70.5"
          y2="7.75001"
          stroke="#8B8B8B"
          strokeWidth="0.5"
        />
        <Mask id="path-2-inside-1_302_5137" fill="white">
          <Path d="M86.0607 0.325832C85.0345 3.80138 82.2979 6.54121 78.8265 7.56457C78.3912 7.69393 78.3912 8.30607 78.8265 8.43543C82.2959 9.45879 85.0345 12.1986 86.0607 15.6742C86.189 16.1086 86.809 16.1086 86.9393 15.6742C87.9655 12.1986 90.7021 9.45879 94.1735 8.43543C94.6088 8.30607 94.6088 7.69393 94.1735 7.56457C90.7041 6.54121 87.9655 3.80138 86.9393 0.325832C86.811 -0.108611 86.191 -0.108611 86.0607 0.325832Z" />
        </Mask>
        <Path
          d="M86.0607 0.325832L85.5818 0.182268L85.5812 0.184238L86.0607 0.325832ZM78.8265 7.56457L78.6851 7.08497L78.6841 7.08528L78.8265 7.56457ZM78.8265 8.43543L78.6841 8.91472L78.6851 8.91501L78.8265 8.43543ZM86.9393 15.6742L87.4182 15.8177L87.4188 15.8158L86.9393 15.6742ZM94.1735 8.43543L94.3149 8.91503L94.3159 8.91472L94.1735 8.43543ZM94.1735 7.56457L94.3159 7.08528L94.3149 7.08499L94.1735 7.56457ZM86.9393 0.325832L87.4188 0.184238L86.9393 0.325832ZM85.5812 0.184238C84.6029 3.49747 81.9933 6.10973 78.6851 7.08497L78.9679 8.04416C82.6025 6.97269 85.4661 4.10528 86.5403 0.467426L85.5812 0.184238ZM78.6841 7.08528C77.772 7.35632 77.772 8.64368 78.6841 8.91472L78.9689 7.95614C78.9667 7.95548 78.9699 7.95615 78.9757 7.96033C78.9814 7.96442 78.9868 7.96984 78.991 7.97589C78.9991 7.98738 79 7.9962 79 8C79 8.0038 78.9991 8.01262 78.991 8.02411C78.9868 8.03016 78.9814 8.03558 78.9757 8.03967C78.9699 8.04385 78.9667 8.04452 78.9689 8.04386L78.6841 7.08528ZM78.6851 8.91501C81.9914 9.89027 84.6029 12.5026 85.5812 15.8158L86.5403 15.5326C85.4661 11.8946 82.6004 9.02731 78.968 7.95586L78.6851 8.91501ZM85.5812 15.8158C85.8516 16.7317 87.1466 16.7238 87.4182 15.8177L86.4603 15.5306C86.4599 15.532 86.4609 15.5283 86.4652 15.5223C86.4695 15.5165 86.4749 15.5113 86.4805 15.5074C86.491 15.5002 86.4979 15.5 86.4993 15.5C86.5006 15.5 86.508 15.5002 86.519 15.5079C86.5249 15.5119 86.5305 15.5174 86.535 15.5235C86.5395 15.5298 86.5406 15.5338 86.5403 15.5326L85.5812 15.8158ZM87.4188 15.8158C88.3971 12.5025 91.0067 9.89027 94.3149 8.91503L94.0321 7.95584C90.3975 9.02731 87.5339 11.8947 86.4597 15.5326L87.4188 15.8158ZM94.3159 8.91472C95.228 8.64368 95.228 7.35632 94.3159 7.08528L94.0311 8.04386C94.0333 8.04452 94.0301 8.04385 94.0243 8.03967C94.0186 8.03558 94.0132 8.03016 94.009 8.02411C94.0009 8.01262 94 8.0038 94 8C94 7.9962 94.0009 7.98738 94.009 7.97589C94.0132 7.96984 94.0186 7.96442 94.0243 7.96033C94.0301 7.95615 94.0333 7.95548 94.0311 7.95614L94.3159 8.91472ZM94.3149 7.08499C91.0086 6.10974 88.3971 3.49737 87.4188 0.184238L86.4597 0.467426C87.5339 4.10538 90.3996 6.97269 94.032 8.04414L94.3149 7.08499ZM87.4188 0.184238C87.1484 -0.731663 85.8534 -0.72384 85.5818 0.182269L86.5397 0.469396C86.5401 0.467985 86.5391 0.47167 86.5348 0.477665C86.5305 0.483504 86.5251 0.488667 86.5195 0.49256C86.509 0.499821 86.5021 0.5 86.5007 0.5C86.4994 0.5 86.492 0.499815 86.481 0.492146C86.4751 0.488052 86.4695 0.482626 86.465 0.47648C86.4605 0.470179 86.4594 0.466215 86.4597 0.467426L87.4188 0.184238Z"
          fill="#8B8B8B"
          mask="url(#path-2-inside-1_302_5137)"
        />
        <Line
          x1="102.5"
          y1="7.75"
          x2="172.5"
          y2="7.75001"
          stroke="#8B8B8B"
          strokeWidth="0.5"
        />
      </Svg>
      <VStack className="px-4">
        <Text className="text-center text-gold-ultralight text-[16px]">
          {perfume.description}
        </Text>
      </VStack>
      <Svg width="311" height="16" viewBox="0 0 311 16" fill="none">
        <G clip-path="url(#clip0_302_5142)">
          <Line
            x1="-25.3984"
            y1="7.75"
            x2="130.602"
            y2="7.75"
            stroke="#8B8B8B"
            stroke-width="0.5"
          />
          <Path
            d="M155.501 14.6359C154.537 11.0389 151.349 8.39101 147.529 8.19905L143.575 7.99999L147.529 7.80093C147.529 7.80093 147.529 7.80093 147.529 7.80093C151.349 7.60896 154.537 4.96112 155.501 1.36413C156.465 4.96112 159.653 7.60896 163.473 7.80093C163.473 7.80093 163.473 7.80093 163.473 7.80093L167.427 7.99999L163.473 8.19905C159.653 8.39101 156.465 11.0389 155.501 14.6359Z"
            stroke="#8B8B8B"
            stroke-width="0.5"
          />
          <Line
            x1="180.398"
            y1="7.75"
            x2="336.398"
            y2="7.75"
            stroke="#8B8B8B"
            strokeWidth="0.5"
          />
        </G>
        <Defs>
          <ClipPath id="clip0_302_5142">
            <Rect width="311" height="16" fill="white" />
          </ClipPath>
        </Defs>
      </Svg>
    </VStack>
  );
}

const styles = StyleSheet.create({
  church: {
    borderTopStartRadius: "100%",
    borderTopEndRadius: "100%",
    paddingTop: 6,
    borderWidth: 1,
  },
});
