import { store } from "@/store";
import { HStack } from "@/components/ui/hstack";
import { TouchableOpacity } from "react-native";
import { Alert } from "@/components/ui/custom-alert";
import StarOutline from "@/assets/images/icons/star-outline.svg";
import StarFilled from "@/assets/images/icons/star-full.svg";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { Image } from "@/components/ui/image";
import { backAssetsUrl } from "@/utils/domain";
import { Heading } from "@/components/ui/heading";
import { Card } from "@/components/ui/card";
import React, { useMemo } from "react";
import { components } from "@/@types/api.types";
import { connect, useSelector } from "react-redux";
import { Pressable } from "@/components/ui/pressable";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useRouter } from "expo-router";

type Props = {
  perfume: components["schemas"]["Perfume"];
  similarity: number;

  perfumesFav: number[];
  addFavorite: (id: number) => void;
  removeFavorite: (id: number) => void;
};

function PerfumeSimilarCard({
  perfume,
  similarity,
  perfumesFav,
  addFavorite,
  removeFavorite,
}: Props) {
  const { t } = useI18n();
  const router = useRouter();
  const isFav = useMemo(
    () => perfumesFav.includes(perfume.id!),
    [perfumesFav, perfume.id]
  );

  // @ts-ignore
  const auth = useSelector((state) => state.auth);

  const toggleFavorite = () => {
    if (!auth.isAuthenticated) {
      Alert.alert(t("common.alert"), t("favorites.errors.not_authenticated"));
    } else {
      if (isFav) {
        removeFavorite(perfume.id!);
      } else {
        addFavorite(perfume.id!);
      }
    }
  };

  const navigateToPerfumeDetail = () => {
    router.push(`/(app)/(tabs)/(search)/search/perfume/${perfume.id}`);
  };

  return (
    <Pressable onPress={navigateToPerfumeDetail}>
      <Card className="p-4 border-gold-ultralight border-[0.5px] border-none flex-1">
        <HStack className="justify-between items-center">
          <TouchableOpacity activeOpacity={0.6} onPress={toggleFavorite}>
            {isFav ? <StarFilled /> : <StarOutline />}
          </TouchableOpacity>
          <Text className="text-grey-300 text-[12px] rounded-3xl border border-grey-600 py-2 px-2">
            {(similarity * 100).toFixed(2)}%
          </Text>
        </HStack>
        <VStack space="lg" className="items-center mt-3">
          {perfume.picture?.url && (
            <Image
              size="2xl"
              source={{ uri: backAssetsUrl(perfume.picture?.url) }}
              alt={perfume.name}
            />
          )}
          <Heading
            numberOfLines={1}
            ellipsizeMode="tail"
            className="text-gold-ultralight text-[24px] mx-8 text-center"
          >
            {perfume.display_name || perfume.name}
          </Heading>
          <Text className="text-grey-200 uppercase -mt-2">
            {perfume.brand?.display_name || perfume.brand?.name}
          </Text>
        </VStack>
      </Card>
    </Pressable>
  );
}

const mapState = store.select((models) => ({
  perfumesFav: models.favorites.ids,
}));
const mapDispatch = (dispatch: any) => ({
  addFavorite: dispatch.favorites.addFavorite,
  removeFavorite: dispatch.favorites.removeFavorite,
});

export default connect(mapState, mapDispatch)(PerfumeSimilarCard);
