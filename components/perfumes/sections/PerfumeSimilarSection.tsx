import { components } from "@/@types/api.types";
import { Heading } from "@/components/ui/heading";
import { VStack } from "@/components/ui/vstack";
import { Path, Svg } from "react-native-svg";
import { HStack } from "@/components/ui/hstack";
import { useWindowDimensions, View, StyleSheet } from "react-native";
import useSWR from "swr";
import Animated, {
  interpolate,
  runOnJS,
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import React, { useEffect, useState } from "react";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { Spinner } from "@/components/ui/spinner";
import PerfumeSimilarCard from "@/components/perfumes/sections/PerfumeSimilarCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

type PerfumeSimilar = {
  perfume: components["schemas"]["Perfume"];
  similarity: number;
};

type Props = {
  perfume: components["schemas"]["Perfume"];
};

function Circle() {
  return (
    <Svg width="168" height="64" viewBox="0 0 168 64" fill="none">
      <Path
        d="M167.75 32C167.75 36.3227 165.451 40.4695 161.232 44.2701C157.012 48.0706 150.892 51.5047 143.308 54.3938C128.142 60.1713 107.174 63.75 84 63.75C60.8265 63.75 39.858 60.1713 24.692 54.3938C17.1082 51.5047 10.9881 48.0706 6.76845 44.2701C2.5487 40.4695 0.25 36.3227 0.25 32C0.25 27.6773 2.5487 23.5305 6.76845 19.7299C10.9881 15.9294 17.1082 12.4953 24.692 9.6062C39.858 3.82869 60.8265 0.25 84 0.25C107.174 0.25 128.142 3.82869 143.308 9.6062C150.892 12.4953 157.012 15.9294 161.232 19.7299C165.451 23.5305 167.75 27.6773 167.75 32Z"
        stroke="#8B8B8B"
        stroke-width="0.5"
      />
    </Svg>
  );
}

export function PerfumeSimilarSection({ perfume: target }: Props) {
  const { data: perfumes, isLoading } = useSWR<PerfumeSimilar[]>(
    `/perfumes/${target.id}/similar`,
    { keepPreviousData: true }
  );

  const [currentIndex, setCurrentIndex] = useState(0);
  const animatedValue = useSharedValue(0);
  const MAX = 3;

  const [copyPerfumes, setCopyPerfumes] = useState<PerfumeSimilar[]>([]);
  useEffect(() => {
    setCopyPerfumes(perfumes || []);
  }, [perfumes]);

  const { t } = useI18n();

  return (
    <VStack space="3xl">
      <VStack className="items-center relative">
        <Heading className="text-gold-ultralight text-[24px] font-heading-bold z-50">
          {t("perfume.olactive_variations")}{" "}
        </Heading>
        <HStack className="absolute -top-1/2 z-10">
          <View style={{ left: "10%" }}>
            <Circle />
          </View>
          <View style={{ right: "10%" }}>
            <Circle />
          </View>
        </HStack>
      </VStack>

      {isLoading && (
        <VStack className="justify-center items-center mt-4">
          <Spinner />
        </VStack>
      )}

      {copyPerfumes.length > 0 && (
        <View className="pb-[450px] flex flex-1 justify-center items-center mt-10">
          {copyPerfumes.map((item, index) => {
            if (index > currentIndex + MAX || index < currentIndex) {
              return null;
            }
            return (
              <PerfumeCard
                newData={copyPerfumes}
                setNewData={setCopyPerfumes}
                maxVisibleItems={MAX}
                item={item}
                index={index}
                dataLength={copyPerfumes.length}
                animatedValue={animatedValue}
                currentIndex={currentIndex}
                setCurrentIndex={setCurrentIndex}
                key={index}
              />
            );
          })}
        </View>
      )}
    </VStack>
  );
}

type PerfumeCardProps = {
  newData: PerfumeSimilar[];
  setNewData: React.Dispatch<React.SetStateAction<any[]>>;
  maxVisibleItems: number;
  item: PerfumeSimilar;
  index: number;
  dataLength: number;
  animatedValue: SharedValue<number>;
  currentIndex: number;
  setCurrentIndex: React.Dispatch<React.SetStateAction<number>>;
};

function PerfumeCard({
  newData,
  setNewData,
  maxVisibleItems,
  item,
  index,
  dataLength,
  animatedValue,
  currentIndex,
  setCurrentIndex,
}: PerfumeCardProps) {
  const { width } = useWindowDimensions();
  const translateX = useSharedValue(0);
  const direction = useSharedValue(0);

  const pan = Gesture.Pan()
    .onUpdate((e) => {
      const isSwipeRight = e.translationX > 0;

      direction.value = isSwipeRight ? 1 : -1;

      if (currentIndex === index) {
        translateX.value = e.translationX;
        animatedValue.value = interpolate(
          Math.abs(e.translationX),
          [0, width],
          [index, index + 1]
        );
      }
    })
    .onEnd((e) => {
      if (currentIndex === index) {
        if (Math.abs(e.translationX) > 150 || Math.abs(e.velocityX) > 1000) {
          translateX.value = withTiming(width * direction.value, {}, () => {
            runOnJS(setNewData)([...newData, newData[currentIndex]]);
            runOnJS(setCurrentIndex)(currentIndex + 1);
          });
          animatedValue.value = withTiming(currentIndex + 1);
        } else {
          translateX.value = withTiming(0, { duration: 500 });
          animatedValue.value = withTiming(currentIndex, { duration: 500 });
        }
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    const currentItem = index === currentIndex;

    const translateY = interpolate(
      animatedValue.value,
      [index - 1, index],
      [-30, 0]
    );

    const scale = interpolate(
      animatedValue.value,
      [index - 1, index],
      [0.9, 1]
    );

    const rotateZ = interpolate(
      Math.abs(translateX.value),
      [0, width],
      [0, 20]
    );

    const opacity = interpolate(
      animatedValue.value + maxVisibleItems,
      [index, index + 1],
      [0, 1]
    );

    return {
      transform: [
        { translateY: currentItem ? 0 : translateY },
        { scale: currentItem ? 1 : scale },
        { translateX: translateX.value },
        {
          rotateZ: currentItem ? `${direction.value * rotateZ}deg` : "0deg",
        },
      ],
      opacity: index < currentIndex + maxVisibleItems ? 1 : opacity,
    };
  });

  return (
    <GestureDetector gesture={pan}>
      <Animated.View
        style={[
          styles.container,
          { zIndex: dataLength - index },
          animatedStyle,
        ]}
        className="w-[90%]"
      >
        <PerfumeSimilarCard
          perfume={item.perfume}
          similarity={item.similarity}
        />
      </Animated.View>
    </GestureDetector>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    borderRadius: 28,
    padding: 16,
  },
});
