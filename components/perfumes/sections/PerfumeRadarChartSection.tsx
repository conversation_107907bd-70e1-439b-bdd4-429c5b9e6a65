import { components } from "@/@types/api.types";
import { Card } from "@/components/ui/card";
import { VStack } from "@/components/ui/vstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { RadarChart } from "@salmonco/react-native-radar-chart";
import { useMemo } from "react";
import { Line, Mask, Path, Svg } from "react-native-svg";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { getNoteCategoryTranslationKey } from "@/utils/notes";

type Props = {
  perfume: components["schemas"]["Perfume"];
};

export function PerfumeRadarChartSection({ perfume }: Props) {
  const { t, locale } = useI18n();
  const constants = useTranslatedConstants();
  const { getFamilyLabel } = constants;

  const data = useMemo<{ label: string; value: number }[]>(() => {
    const groupedNotes = perfume.notes!.reduce(
      (acc, note) => {
        // @ts-ignore
        const categoryKey = note.categoryId || "uncategorized";

        if (!acc[categoryKey]) {
          acc[categoryKey] = {
            category: note.category || {
              id: categoryKey,
              name: "Uncategorized",
            },
            notes: [],
          };
        }

        acc[categoryKey].notes.push(note);
        return acc;
      },
      {} as Record<
        string | number,
        {
          category: components["schemas"]["NoteCategory"];
          notes: components["schemas"]["Note"][];
        }
      >,
    );

    const uniqueLabels = new Set();

    return Object.values(groupedNotes)
      .map(({ category, notes }) => {
        // Get the note category name
        // @ts-ignore
        const categoryName = category?.name || "uncategorized";
        
        // Get translation key using the utility function with category name
        const translationKey = getNoteCategoryTranslationKey(String(categoryName));
        
        // Try to get translation using the proper key, fallback to display name or name if not found
        const categoryLabel = t(translationKey) !== translationKey 
          ? t(translationKey)
          // @ts-ignore - Fallback to API values if translation is not found
          : category.displayName || category.name;
        
        return {
          label: categoryLabel,
          // @ts-ignore
          value: notes.reduce((acc, note) => acc + note.percentage, 0),
        };
      })
      .filter(({ label }) => {
        if (uniqueLabels.has(label)) {
          return false;
        }
        uniqueLabels.add(label);
        return true;
      });
  }, [perfume.notes, locale]); // Add locale as dependency to update when language changes

  return (
    <Card className="rounded-none">
      <VStack className="items-center" space="xl">
        <VStack className="items-center" space="sm">
          <Heading className="text-gold-ultralight font-heading-bold text-[24px] mt-3">
            {t("perfume.olfactory_profile_title")}
          </Heading>
          {perfume.family && (
            <Text className="text-gold-ultralight font-body-semibold text-[18px]">
              {getFamilyLabel(perfume.family)}
            </Text>
          )}
        </VStack>

        <RadarChart
          data={data}
          maxValue={100}
          labelSize={13}
          gradientColor={{
            startColor: "#232323",
            endColor: "#232323",
            count: 3,
          }}
          stroke={["#FFE8D3", "#FFE8D3", "#FFE8D3", "#FFE8D3", "#ff9532"]}
          labelColor="#A2A2A2"
          dataFillColor="#F1CA90"
          dataFillOpacity={0.8}
          dataStroke="#F1CA90"
          dataStrokeWidth={1}
          divisionStroke="#F1CA90"
          divisionStrokeWidth={0.8}
          divisionStrokeOpacity={0.5}
          strokeWidth={[0.2, 0.2, 0.2, 0.2, 0.2]}
          strokeOpacity={[0.6, 0.6, 0.6, 0.6, 0.6]}
        />

        <VStack className="pb-6">
          <Svg width="173" height="16" viewBox="0 0 173 16" fill="none">
            <Line
              x1="0.5"
              y1="7.75"
              x2="70.5"
              y2="7.75001"
              stroke="#8B8B8B"
              stroke-width="0.5"
            />
            <Mask id="path-2-inside-1_302_5113" fill="white">
              <Path d="M86.0607 0.325832C85.0345 3.80138 82.2979 6.54121 78.8265 7.56457C78.3912 7.69393 78.3912 8.30607 78.8265 8.43543C82.2959 9.45879 85.0345 12.1986 86.0607 15.6742C86.189 16.1086 86.809 16.1086 86.9393 15.6742C87.9655 12.1986 90.7021 9.45879 94.1735 8.43543C94.6088 8.30607 94.6088 7.69393 94.1735 7.56457C90.7041 6.54121 87.9655 3.80138 86.9393 0.325832C86.811 -0.108611 86.191 -0.108611 86.0607 0.325832Z" />
            </Mask>
            <Path
              d="M86.0607 0.325832L85.5818 0.182268L85.5812 0.184238L86.0607 0.325832ZM78.8265 7.56457L78.6851 7.08497L78.6841 7.08528L78.8265 7.56457ZM78.8265 8.43543L78.6841 8.91472L78.6851 8.91501L78.8265 8.43543ZM86.9393 15.6742L87.4182 15.8177L87.4188 15.8158L86.9393 15.6742ZM94.1735 8.43543L94.3149 8.91503L94.3159 8.91472L94.1735 8.43543ZM94.1735 7.56457L94.3159 7.08528L94.3149 7.08499L94.1735 7.56457ZM86.9393 0.325832L87.4188 0.184238L86.9393 0.325832ZM85.5812 0.184238C84.6029 3.49747 81.9933 6.10973 78.6851 7.08497L78.9679 8.04416C82.6025 6.97269 85.4661 4.10528 86.5403 0.467426L85.5812 0.184238ZM78.6841 7.08528C77.772 7.35632 77.772 8.64368 78.6841 8.91472L78.9689 7.95614C78.9667 7.95548 78.9699 7.95615 78.9757 7.96033C78.9814 7.96442 78.9868 7.96984 78.991 7.97589C78.9991 7.98738 79 7.9962 79 8C79 8.0038 78.9991 8.01262 78.991 8.02411C78.9868 8.03016 78.9814 8.03558 78.9757 8.03967C78.9699 8.04385 78.9667 8.04452 78.9689 8.04386L78.6841 7.08528ZM78.6851 8.91501C81.9914 9.89027 84.6029 12.5026 85.5812 15.8158L86.5403 15.5326C85.4661 11.8946 82.6004 9.02731 78.968 7.95586L78.6851 8.91501ZM85.5812 15.8158C85.8516 16.7317 87.1466 16.7238 87.4182 15.8177L86.4603 15.5306C86.4599 15.532 86.4609 15.5283 86.4652 15.5223C86.4695 15.5165 86.4749 15.5113 86.4805 15.5074C86.491 15.5002 86.4979 15.5 86.4993 15.5C86.5006 15.5 86.508 15.5002 86.519 15.5079C86.5249 15.5119 86.5305 15.5174 86.535 15.5235C86.5395 15.5298 86.5406 15.5338 86.5403 15.5326L85.5812 15.8158ZM87.4188 15.8158C88.3971 12.5025 91.0067 9.89027 94.3149 8.91503L94.0321 7.95584C90.3975 9.02731 87.5339 11.8947 86.4597 15.5326L87.4188 15.8158ZM94.3159 8.91472C95.228 8.64368 95.228 7.35632 94.3159 7.08528L94.0311 8.04386C94.0333 8.04452 94.0301 8.04385 94.0243 8.03967C94.0186 8.03558 94.0132 8.03016 94.009 8.02411C94.0009 8.01262 94 8.0038 94 8C94 7.9962 94.0009 7.98738 94.009 7.97589C94.0132 7.96984 94.0186 7.96442 94.0243 7.96033C94.0301 7.95615 94.0333 7.95548 94.0311 7.95614L94.3159 8.91472ZM94.3149 7.08499C91.0086 6.10974 88.3971 3.49737 87.4188 0.184238L86.4597 0.467426C87.5339 4.10538 90.3996 6.97269 94.032 8.04414L94.3149 7.08499ZM87.4188 0.184238C87.1484 -0.731663 85.8534 -0.72384 85.5818 0.182269L86.5397 0.469396C86.5401 0.467985 86.5391 0.47167 86.5348 0.477665C86.5305 0.483504 86.5251 0.488667 86.5195 0.49256C86.509 0.499821 86.5021 0.5 86.5007 0.5C86.4994 0.5 86.492 0.499815 86.481 0.492146C86.4751 0.488052 86.4695 0.482626 86.465 0.47648C86.4605 0.470179 86.4594 0.466215 86.4597 0.467426L87.4188 0.184238Z"
              fill="#8B8B8B"
              mask="url(#path-2-inside-1_302_5113)"
            />
            <Line
              x1="102.5"
              y1="7.75"
              x2="172.5"
              y2="7.75001"
              stroke="#8B8B8B"
              stroke-width="0.5"
            />
          </Svg>
        </VStack>
      </VStack>
    </Card>
  );
}
