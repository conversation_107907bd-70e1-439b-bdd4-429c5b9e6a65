import { components } from "@/@types/api.types";
import React, { useMemo, useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { Card } from "@/components/ui/card";
import { VStack } from "@/components/ui/vstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import DividerSvg from "@/components/layout/Divider";
import { Image } from "@/components/ui/image";
import { backAssetsUrl } from "@/utils/domain";
import { HStack } from "@/components/ui/hstack";
import { Divider } from "@/components/ui/divider";
import { TouchableOpacity, View } from "react-native";
import { PerfumeChips } from "../PerfumeChips";
import { PerfumePriceComparisonSection } from "./PerfumePriceComparisonSection";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useCurrency } from "@/providers/currency/currency.provider";
import { ShoppingBagIcon, LoaderIcon, ExternalLinkIcon } from "lucide-react-native";
import { useLinkHandler } from "@/hooks/useLinkHandler";
import { useSelector } from "react-redux";
import { useRouter } from "expo-router";
import brandsData from "@/assets/json/brands.json";

type Props = {
  perfume: components["schemas"]["Perfume"];
};

export function PerfumeInfoSection({ perfume }: Props) {
  const [showAllVendors, setShowAllVendors] = useState(false);
  const constants = useTranslatedConstants();
  const { PERFUME_TYPES, getGenderLabel } = constants;
  const { t } = useI18n();
  const { convertPrice, formatCurrency } = useCurrency();
  const router = useRouter();
  // @ts-ignore
  const auth = useSelector((state) => state.auth);

  const {
    openLink,
    isLoading: isPurchasing,
    redirectingText,
    buyNowText,
  } = useLinkHandler({
    loadingDelay: 100,
    resetDelay: 100,
  });

  const variantsTabs = useMemo<
    { size: number; price: number | undefined }[]
  >(() => {
    if (!perfume.variants) {
      return [];
    }
    return Object.values(
      perfume.variants.reduce<{
        [key: string]: { size: number; price: number | undefined };
      }>((acc, variant) => {
        if (!variant.size) {
          return acc;
        }
        const key = `${variant.size}`;
        if (acc[key]) {
          if (
            variant.price &&
            (!acc[key].price || variant.price < acc[key].price)
          ) {
            acc[key].price = variant.price;
          }
        } else {
          acc[key] = { size: variant.size, price: variant.price };
        }

        return acc;
      }, {})
    );
  }, [perfume.variants]);

  const [selectedVariant, setSelectedVariant] = useState<{
    size: number;
    price: number | undefined;
  }>(variantsTabs[0]);

  const handleToggleShowAllVendors = () => {
    setShowAllVendors(!showAllVendors);
  };

  // Function to check if vendor is a mother house
  const isVendorMotherHouse = (vendor: string, brandName: string): boolean => {
    // Normalize strings for comparison
    const normalizedVendor = vendor.toLowerCase().trim();
    const normalizedBrand = brandName.toLowerCase().trim();

    // Check if vendor name contains brand name or vice versa
    return (
      normalizedVendor.includes(normalizedBrand) ||
      normalizedBrand.includes(normalizedVendor)
    );
  };

  // Handle opening the link to purchase
  const handleBuyNow = () => {
    if (selectedVariant && perfume.variants) {
      // Find the cheapest variant for the selected size
      const cheapestVariant = perfume.variants
        .filter(
          (v) =>
            v.size === selectedVariant.size && v.price && v.vendor && v.link
        )
        .sort((a, b) => (a.price || 0) - (b.price || 0))[0];

      if (cheapestVariant && cheapestVariant.link) {
        openLink(cheapestVariant.link, "buy-now-button");
      }
    }
  };

  const navigateToRegister = () => {
    router.push("/onboarding");
  };

  // Get brand website link from brands.json using brand ID
  const getBrandWebsiteLink = (brandId: number | undefined): string | null => {
    if (!brandId) return null;
    const brand = brandsData.find(b => b.id === brandId);
    return brand?.link || null;
  };

  const brandWebsiteLink = getBrandWebsiteLink(perfume.brand?.id);

  console.log("perfume.variants", JSON.stringify(perfume.variants, null, 2));
  console.log("variantsTabs", JSON.stringify(variantsTabs, null, 2));

  return (
    <LinearGradient
      colors={["#F1CA90", "#BF9E57"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ padding: 1 }}
    >
      <Card className="p-0 pb-8 rounded-none">
        <VStack space="lg">
          <VStack className="items-center pt-8" space="md">
            <Heading className="text-[24px] font-heading-bold">
              {perfume.display_name || perfume.name}
            </Heading>
            <Text className="text-grey-200">
              {(
                perfume.brand?.display_name ||
                perfume.brand?.name ||
                ""
              ).toUpperCase()}
            </Text>
          </VStack>
          <DividerSvg />
          <VStack className="items-center mb-1" space="xl">
            {perfume.picture?.url && (
              <Image
                size="2xl"
                source={{ uri: backAssetsUrl(perfume.picture?.url) }}
                alt={perfume.name}
              />
            )}
            <HStack space="sm">
              {perfume.year && <PerfumeChips content={perfume.year} />}
              {perfume.type && (
                <PerfumeChips
                  content={PERFUME_TYPES[perfume.type].toLocaleUpperCase()}
                />
              )}
              {perfume.gender && (
                <PerfumeChips
                  content={getGenderLabel(perfume.gender).toLocaleUpperCase()}
                />
              )}
            </HStack>
          </VStack>

          {variantsTabs.length > 0 && (
            <>
              <Divider />

              <VStack className="px-6 pt-4">
                {/* Size selector */}
                <HStack space="lg" className="flex-wrap justify-center mb-4">
                  {variantsTabs.map((variant) => (
                    <TouchableOpacity
                      key={variant.size}
                      onPress={() => setSelectedVariant(variant)}
                      activeOpacity={0.6}
                    >
                      <Text
                        className={
                          selectedVariant.size === variant.size
                            ? "text-gold-ultralight font-body-bold border-b-2 border-[#BF9E57] px-2 py-1"
                            : "text-grey-400 border-b-2 border-transparent px-2 py-1"
                        }
                      >
                        {variant.size.toString()}ml
                      </Text>
                    </TouchableOpacity>
                  ))}
                </HStack>

                {/* Best price highlight or registration call */}
                {!auth.isAuthenticated ? (
                  <VStack space="md" className="mt-2">
                    <LinearGradient
                      colors={["rgba(241,202,144, 1)", "rgba(191,158,87,1)"]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      className="border-[0.5px] border-gold-ultralight/40 shadow-sm overflow-hidden"
                    >
                      <View className="px-5 py-5">
                        <VStack space="md">
                          <Text className="text-black/90 text-md text-center">
                            {t("perfume.register_to_see_prices")}
                          </Text>

                          <TouchableOpacity
                            className="mt-2 py-2.5 bg-gold-ultralight/30 border-[0.5px] border-gold-ultralight/30"
                            onPress={navigateToRegister}
                            activeOpacity={0.6}
                          >
                            <HStack className="justify-center items-center">
                              <HStack
                                space="sm"
                                className="items-center justify-center"
                              >
                                <Text className="text-black/90 font-body-bold text-base">
                                  {t("auth.register")}
                                </Text>
                              </HStack>
                            </HStack>
                          </TouchableOpacity>
                        </VStack>
                      </View>
                    </LinearGradient>
                  </VStack>
                ) : (
                  selectedVariant &&
                  selectedVariant.price && (
                    <VStack space="md" className="mb-4 mt-2">
                      <LinearGradient
                        colors={[
                          "rgba(241,202,144, 0.9)",
                          "rgba(191,158,87,0.9)",
                        ]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        className="border-[0.5px] border-gold-ultralight/40 shadow-sm overflow-hidden"
                      >
                        <View className="px-5 py-5">
                          <HStack className="justify-between items-center">
                            <VStack>
                              <HStack space="xs" className="items-center">
                                <Text className="text-black/90 font-body-semibold text-base">
                                  {(perfume.variants &&
                                    perfume.variants
                                      .filter(
                                        (v) =>
                                          v.size === selectedVariant.size &&
                                          v.price &&
                                          v.vendor
                                      )
                                      .sort(
                                        (a, b) =>
                                          (a.price || 0) - (b.price || 0)
                                      )[0]?.vendor) ||
                                    `${selectedVariant.size}ml`}
                                </Text>
                                {perfume.variants &&
                                  perfume.brand?.name &&
                                  perfume.variants
                                    .filter(
                                      (v) =>
                                        v.size === selectedVariant.size &&
                                        v.price &&
                                        v.vendor
                                    )
                                    .sort(
                                      (a, b) => (a.price || 0) - (b.price || 0)
                                    )[0]?.vendor &&
                                  isVendorMotherHouse(
                                    perfume.variants
                                      .filter(
                                        (v) =>
                                          v.size === selectedVariant.size &&
                                          v.price &&
                                          v.vendor
                                      )
                                      .sort(
                                        (a, b) =>
                                          (a.price || 0) - (b.price || 0)
                                      )[0]?.vendor || "",
                                    perfume.brand?.name
                                  ) && (
                                    <View className="bg-black/60 px-1.5 py-0.5 ml-1">
                                      <Text className="text-gold-ultralight italic text-[10px]">
                                        {t("perfume.mother_house")}
                                      </Text>
                                    </View>
                                  )}
                              </HStack>
                              <HStack space="xs" className="items-center">
                                <Text className="text-black/90 text-xs">
                                  {selectedVariant.price && selectedVariant.size
                                    ? formatCurrency(
                                        convertPrice(
                                          selectedVariant.price /
                                            selectedVariant.size
                                        )
                                      )
                                    : "0.00"}
                                </Text>
                                <Text className="text-black/90 text-[10px]">
                                  / ml
                                </Text>
                              </HStack>
                            </VStack>
                            <Text className="text-black/90 font-body-bold text-2xl">
                              {formatCurrency(
                                convertPrice(selectedVariant.price)
                              )}
                            </Text>
                          </HStack>

                          <TouchableOpacity
                            className={`mt-2 py-2.5 ${
                              isPurchasing
                                ? "bg-gold-ultralight/20"
                                : "bg-gold-ultralight/30"
                            } border-[0.5px] border-gold-ultralight/30`}
                            onPress={handleBuyNow}
                            activeOpacity={0.6}
                            disabled={isPurchasing}
                          >
                            <HStack className="justify-center items-center">
                              {isPurchasing ? (
                                <HStack
                                  space="sm"
                                  className="items-center justify-center"
                                >
                                  <LoaderIcon
                                    width={20}
                                    height={20}
                                    color="#181718"
                                    strokeWidth={2}
                                  />
                                  <Text className="text-black/90 font-body-bold text-base">
                                    {redirectingText}
                                  </Text>
                                </HStack>
                              ) : (
                                <HStack
                                  space="sm"
                                  className="items-center justify-center"
                                >
                                  <ShoppingBagIcon
                                    width={18}
                                    height={18}
                                    color="#181718"
                                    strokeWidth={2}
                                  />
                                  <Text className="text-black/90 font-body-bold text-base">
                                    {buyNowText}
                                  </Text>
                                </HStack>
                              )}
                            </HStack>
                          </TouchableOpacity>
                        </View>
                      </LinearGradient>
                    </VStack>
                  )
                )}

                {/* Price comparison section */}
                {auth.isAuthenticated && selectedVariant && (
                  <PerfumePriceComparisonSection
                    perfume={perfume}
                    selectedSize={selectedVariant.size}
                    showCompactView={true}
                    showAllVendors={showAllVendors}
                    onShowAllVendors={handleToggleShowAllVendors}
                  />
                )}
              </VStack>
            </>
          )}

          {/* Independent Brand Website Section */}
          {variantsTabs.length === 0 && brandWebsiteLink && (
            <>
              <Divider />
              <VStack space="md" className="px-6 pt-4">
                <LinearGradient
                  colors={["rgba(241,202,144, 0.9)", "rgba(191,158,87,0.9)"]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  className="border-[0.5px] border-gold-ultralight/40 shadow-sm overflow-hidden"
                >
                  <View className="px-5 py-5">
                    <VStack space="md">
                      <View>
                        <Text className="text-black/90 text-lg font-heading-bold text-center">
                          {t("brand.official_website")}
                        </Text>
                        <Text className="text-black/80 text-sm text-center">
                          {t("brand.visit_official_website")}
                        </Text>
                      </View>
                      <TouchableOpacity
                        className="mt-2 py-2.5 bg-gold-ultralight/30 border-[0.5px] border-gold-ultralight/30"
                        onPress={() => openLink(brandWebsiteLink, perfume.brand?.name || "Brand")}
                        activeOpacity={0.6}
                      >
                        <HStack className="justify-center items-center">
                          <ExternalLinkIcon size={18} color="#181718" strokeWidth={2} />
                          <Text className="ml-2 text-black/90 font-body-bold text-base">
                            {t("brand.visit_website")}
                          </Text>
                        </HStack>
                      </TouchableOpacity>
                    </VStack>
                  </View>
                </LinearGradient>
              </VStack>
            </>
          )}
        </VStack>
      </Card>
    </LinearGradient>
  );
}
