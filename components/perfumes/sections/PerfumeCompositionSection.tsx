import { components } from "@/@types/api.types";
import { VStack } from "@/components/ui/vstack";
import { Divider } from "@/components/ui/divider";
import StarIcon from "@/assets/images/icons/star-point.svg";
import { Heading } from "@/components/ui/heading";
import { HStack } from "@/components/ui/hstack";
import { useMemo, useRef, useState } from "react";
import { Text } from "@/components/ui/text";
import InfoRounded from "@/assets/images/icons/info-rounded.svg";
import {
  Animated,
  Dimensions,
  ImageSourcePropType,
  TouchableOpacity,
} from "react-native";
import { ScrollView } from "@/components/ui/scroll-view";
import { Card } from "@/components/ui/card";
import { Image } from "@/components/ui/image";
import { HorizontalScrollDots } from "@/components/layout/HorizontalScrollDots";
import {
  Modal,
  ModalBackdrop,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
} from "@/components/ui/modal";
import { CloseIcon, Icon } from "@/components/ui/icon";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { getNoteTranslationKey } from "@/utils/notes";

type Props = {
  perfume: components["schemas"]["Perfume"];
};

export function PerfumeCompositionSection({ perfume }: Props) {
  const { t } = useI18n();

  const headNotes = useMemo(
    // @ts-ignore
    () => perfume.notes?.filter((v) => v.type === "top"),
    [perfume]
  );

  const backNotes = useMemo(
    // @ts-ignore
    () => perfume.notes?.filter((v) => v.type === "base"),
    [perfume]
  );

  const heartNotes = useMemo(
    // @ts-ignore
    () => perfume.notes?.filter((v) => v.type === "middle"),
    [perfume]
  );

  return (
    <VStack space="2xl">
      <VStack>
        <Divider />
        <HStack space="xl" className="items-center justify-center my-3">
          <StarIcon color="#F2ECE0" />
          <Heading className="text-gold-ultralight text-[24px]">
            {t("perfume.composition_title")}
          </Heading>
          <StarIcon color="#F2ECE0" />
        </HStack>
        <Divider />
      </VStack>

      {headNotes && headNotes.length > 0 && (
        <NoteCard
          // @ts-ignore
          notes={headNotes}
          title={t("perfume.head_notes_title")}
          info={t("perfume.head_notes_info")}
        />
      )}
      {heartNotes && heartNotes.length > 0 && (
        <NoteCard
          // @ts-ignore
          notes={heartNotes}
          title={t("perfume.heart_notes_title")}
          info={t("perfume.heart_notes_info")}
        />
      )}
      {backNotes && backNotes.length > 0 && (
        <NoteCard
          // @ts-ignore
          notes={backNotes}
          title={t("perfume.base_notes_title")}
          info={t("perfume.base_notes_info")}
        />
      )}
    </VStack>
  );
}

function NoteCard({
  notes,
  title,
  info,
}: {
  notes: (components["schemas"]["Note"] & {
    percentage: number;
    cover: string | null;
  })[];
  title: string;
  info: string;
}) {
  const screenWidth = Dimensions.get("window").width;
  const scrollX = useRef(new Animated.Value(0)).current;

  const [showInfoModal, setShowInfoModal] = useState(false);
  const { t } = useI18n();

  console.log(JSON.stringify(notes, null, 2));

  return (
    <VStack space="lg">
      <HStack className="items-center justify-between">
        <Text className="text-gold-ultralight text-[16px]">
          {title} ({notes.length})
        </Text>

        <Modal
          isOpen={showInfoModal}
          onClose={() => setShowInfoModal(false)}
          size="lg"
        >
          <ModalBackdrop />
          <ModalContent>
            <ModalHeader>
              <Heading size="md" className="text-typography-950">
                {title}
              </Heading>
              <ModalCloseButton onPress={() => setShowInfoModal(false)}>
                <Icon
                  as={CloseIcon}
                  size="md"
                  className="stroke-background-400 group-[:hover]/modal-close-button:stroke-background-700 group-[:active]/modal-close-button:stroke-background-900 group-[:focus-visible]/modal-close-button:stroke-background-900"
                />
              </ModalCloseButton>
            </ModalHeader>
            <ModalBody>
              <Text size="sm" className="text-gold-ultralight mt-2">
                {info}
              </Text>
            </ModalBody>
          </ModalContent>
        </Modal>

        <TouchableOpacity
          activeOpacity={0.6}
          onPress={() => setShowInfoModal(true)}
        >
          <InfoRounded />
        </TouchableOpacity>
      </HStack>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
      >
        {notes
          .sort((a, b) => b.percentage - a.percentage)
          .map((note, index) => (
            <Card
              className="bg-grey-card items-center justify-center rounded-none"
              key={index}
              style={{
                width: screenWidth / 2 - 20,
                height: screenWidth / 2 - 34,
                marginRight: index !== notes.length - 1 ? 8 : 0,
              }}
            >
              <VStack className="relative items-center">
                <NoteImage
                  link={
                    note.cover
                      ? // @ts-ignore
                        `${process.env.EXPO_PUBLIC_API_URL}${note.cover.url}`
                      : note.id
                        ? `${process.env.EXPO_PUBLIC_API_URL}/storage/uploads/notes/${note.id}.webp`
                        : null
                  }
                  alt={
                    t(getNoteTranslationKey(note.name!)) ||
                    note.display_name ||
                    note.name!
                  }
                />

                <Heading className="font-heading-extrabold text-[40px] text-[#BF9E57] absolute top-3/4">
                  {note.percentage}%
                </Heading>
              </VStack>

              <Text className="font-body-semibold text-[16px] mt-9">
                {t(getNoteTranslationKey(note.name!)) ||
                  note.display_name ||
                  note.name}
              </Text>
            </Card>
          ))}
      </ScrollView>

      {notes.length > 2 && (
        <VStack className="mt-2">
          <HorizontalScrollDots scrollX={scrollX} length={notes.length / 2} />
        </VStack>
      )}
    </VStack>
  );
}

function NoteImage({
  link,
  alt,
}: {
  link: string | ImageSourcePropType | null;
  alt: string;
}) {
  const [hasError, setHasError] = useState(false);

  if (hasError || link === null) {
    return (
      <Image
        size="lg"
        source={require("@/assets/images/perfume-infos/unknown.jpg")}
        alt={alt}
        className="w-16 h-16 rounded-full"
      />
    );
  }

  return (
    <Image
      size="lg"
      source={typeof link === "string" ? { uri: link } : link}
      defaultSource={require("@/assets/images/perfume-infos/unknown.jpg")}
      alt={alt}
      onError={() => setHasError(true)}
      className="w-16 h-16 rounded-full"
    />
  );
}
