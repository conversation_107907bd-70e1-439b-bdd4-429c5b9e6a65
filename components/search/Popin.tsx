import React, { useRef, useState } from "react";
import { StyleSheet, View, FlatList, TouchableOpacity } from "react-native";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import { VStack } from "@/components/ui/vstack";
import Divider from "@/components/layout/Divider";
import { Text } from "../ui/text";
import { Heading } from "../ui/heading";
import ArrowDownIcon from "@/assets/images/icons/chevron_down.svg";
import { SectionTitle } from "./SectionTitle";
import { SearchInput } from "./SearchInput";

type Props = {
  title: string;
  description: string;
  placeholder: string;
  data: { name: string; [key: string]: any }[];
  renderItem: ({ item }: { item: any }) => JSX.Element;
  onSearch: (query: string) => void;
};

export function Popin({
  title,
  description,
  placeholder,
  data,
  renderItem,
  onSearch,
}: Props) {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const handleOpenBottomSheet = () => {
    bottomSheetRef.current?.snapToIndex(0);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    onSearch(text);
  };

  return (
    // <SafeAreaView className="flex-1">
    <View style={styles.container}>
      <SectionTitle title={title} />
      <TouchableOpacity
        onPress={handleOpenBottomSheet}
        style={styles.selectContainer}
      >
        <Text style={styles.selectText}>{placeholder}</Text>
        <ArrowDownIcon width={20} height={20} />
      </TouchableOpacity>

      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={["75%"]}
        index={-1}
        enablePanDownToClose={true}
        backgroundStyle={{
          backgroundColor: "#171717",
          borderRadius: 16,
        }}
        handleIndicatorStyle={{
          backgroundColor: "#D1D1D1",
          width: 50,
        }}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <VStack style={{ padding: 16 }}>
            <Heading className="text-gold-ultralight font-heading-bold text-[24px]">
              {title}
            </Heading>
            <Text className="text-gold-ultralight mt-4 mb-4 text-[16px]">
              {description}
            </Text>

            <SearchInput
              searchQuery={searchQuery}
              handleSearch={handleSearch}
              placeholder={placeholder}
            />
          </VStack>

          <FlatList
            className="bg-background-dark"
            ListHeaderComponent={<Divider />}
            data={data}
            keyExtractor={(item) => item.name}
            numColumns={2}
            renderItem={renderItem}
            columnWrapperStyle={{
              justifyContent: "space-between",
              marginTop: 10,
            }}
            contentContainerStyle={{
              paddingVertical: 16,
              paddingHorizontal: 11,
            }}
            showsVerticalScrollIndicator={false}
          />
        </BottomSheetView>
      </BottomSheet>
    </View>
    // </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  selectContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderWidth: 1,
    borderColor: "#A2A2A2",
    backgroundColor: "#171717",
    width: "100%",
  },
  selectText: {
    fontSize: 14,
    color: "#747474",
  },
  container: {
    flex: 1,
    padding: 16,
  },
  bottomSheetContent: {
    flex: 1,
    padding: 0,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
  },
});
