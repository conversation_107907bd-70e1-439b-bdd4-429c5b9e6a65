import React from "react";
import { View } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";

export function SkeletonCard() {
  return (
    <HStack className="bg-grey-card p-4 rounded-md mb-4 w-full items-center">
      <View className="w-16 h-16 bg-background-dark rounded-md"></View>
      <VStack className="flex-1 ml-4 space-y-2">
        <View className="w-3/4 h-4 bg-background-dark rounded-md mb-2"></View>
        <View className="w-1/2 h-4 bg-background-dark rounded-md"></View>
        <HStack className="space-x-2 mt-2">
          <View className="w-10 h-4 bg-background-dark rounded-md mr-1.5"></View>
          <View className="w-14 h-4 bg-background-dark rounded-md mr-1.5"></View>
          <View className="w-12 h-4 bg-background-dark rounded-md mr-1.5"></View>
        </HStack>
      </VStack>
    </HStack>
  );
}

export function SkeletonList({ count = 5 }: { count?: number }) {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </>
  );
}
