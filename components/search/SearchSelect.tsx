import React, { FC } from "react";
import { FieldValues } from "react-hook-form";
import ArrowDownIcon from "@/assets/images/icons/chevron_down.svg";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from "../ui/select";

import { SectionTitle } from "./SectionTitle";
import { VStack } from "../ui/vstack";

export interface AppSelectProps extends FieldValues {
  title: string;
  placeholder: string;
  data: { label: string; value: string }[];
  containerClassName?: string;
  onChange: (value: string) => void;
  value: string;
}

const AppSelect: FC<AppSelectProps> = ({
  title,
  placeholder,
  data,
  containerClassName,
  onChange,
  value,
  ...props
}) => {
  return (
    <VStack className="w-full py-3">
      <SectionTitle title={title} />
      <Select className="rounded-none" onValueChange={onChange} value={value}>
        <SelectTrigger
          variant="outline"
          size="md"
          className="justify-between pr-4 bg-grey-card rounded-none h-[60px]"
        >
          <SelectInput
            placeholder={placeholder}
            size="md"
            className={`rounded-none text-gold-ultralight ${containerClassName}`}
          />
          <ArrowDownIcon width={20} height={20} stroke="#F2ECE0" />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            {data.map((item, index) => (
              <SelectItem
                key={index}
                label={item.label}
                value={item.value}
                className="h-[50px]"
              />
            ))}
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

export default AppSelect;
