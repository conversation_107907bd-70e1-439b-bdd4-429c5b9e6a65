import { Heading } from "@/components/ui/heading";
import Star from "@/assets/images/icons/star-point.svg";
import { HStack } from "../ui/hstack";

type Props = {
  title: string;
};

export function SectionTitle({ title }: Props) {
  return (
    <HStack className="gap-2 pb-2 items-center">
      <Star color="#F2ECE0" />
      <Heading
        className="text-gold-ultralight text-lg"
        style={{ fontFamily: "serif" }}
      >
        {title}
      </Heading>
    </HStack>
  );
}
