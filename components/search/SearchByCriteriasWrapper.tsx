import React, { useState, useImperativeHandle, forwardRef } from "react";
import { View, TouchableOpacity, Text } from "react-native";
import { HStack } from "@/components/ui/hstack";
import SearchByCriterias, { SearchByCriteriasRef } from "./SearchByCriterias";
import SearchByCriteriasSimple from "./SearchByCriteriasSimple";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useDispatch, useSelector } from "react-redux";

export type { SearchByCriteriasRef } from "./SearchByCriterias";

interface SearchByCriteriasWrapperProps {
  onOpenBottomSheet: (key: string) => void;
  onCloseBottomSheet: VoidFunction;
  family?: string;
}

const SearchByCriteriasWrapper = forwardRef<
  SearchByCriteriasRef,
  SearchByCriteriasWrapperProps
>(({ onOpenBottomSheet, onCloseBottomSheet, family }, ref) => {
  const { t } = useI18n();
  const dispatch = useDispatch();
  // @ts-ignore
  const activeSubTab = useSelector((state) => state.search.activeSubTab);
  
  const searchCriteriasRef = React.useRef<SearchByCriteriasRef>(null);
  const searchCriteriasSimpleRef = React.useRef<SearchByCriteriasRef>(null);

  useImperativeHandle(ref, () => ({
    changeFilters: (id: string, data: any, shouldCloseBottomSheet?: boolean) => {
      if (activeSubTab === "advanced" && searchCriteriasRef.current) {
        searchCriteriasRef.current.changeFilters(id, data, shouldCloseBottomSheet);
      } else if (activeSubTab === "simple" && searchCriteriasSimpleRef.current) {
        searchCriteriasSimpleRef.current.changeFilters(id, data, shouldCloseBottomSheet);
      }
    },
  }));

  return (
    <View className="flex-1">
      <HStack className="border-b-[0.5px] border-grey-500 bg-grey-card">
        <TouchableOpacity
          className={`flex-1 items-center justify-center py-3 ${
            activeSubTab === "simple"
              ? "border-b-[1px] border-[#BF9E57] bg-grey-card"
              : "bg-black"
          }`}
          onPress={() => dispatch.search.setActiveSubTab("simple")}
        >
          <Text
            className={`text-base ${
              activeSubTab === "simple"
                ? "text-gold-ultralight font-bold"
                : "text-gray-400"
            }`}
            allowFontScaling={false}
          >
            {t("search.simple")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center justify-center py-3 ${
            activeSubTab === "advanced"
              ? "border-b-[1px] border-[#BF9E57] bg-grey-card"
              : "bg-black"
          }`}
          onPress={() => dispatch.search.setActiveSubTab("advanced")}
        >
          <Text
            className={`text-base ${
              activeSubTab === "advanced"
                ? "text-gold-ultralight font-bold"
                : "text-gray-400"
            }`}
            allowFontScaling={false}
          >
            {t("search.advanced")}
          </Text>
        </TouchableOpacity>
      </HStack>

      {activeSubTab === "advanced" ? (
        <SearchByCriterias
          ref={searchCriteriasRef}
          onOpenBottomSheet={onOpenBottomSheet}
          onCloseBottomSheet={onCloseBottomSheet}
          family={family}
        />
      ) : (
        <SearchByCriteriasSimple
          ref={searchCriteriasSimpleRef}
          onOpenBottomSheet={onOpenBottomSheet}
          onCloseBottomSheet={onCloseBottomSheet}
          family={family}
        />
      )}
    </View>
  );
});

SearchByCriteriasWrapper.displayName = "SearchByCriteriasWrapper";
export default SearchByCriteriasWrapper;