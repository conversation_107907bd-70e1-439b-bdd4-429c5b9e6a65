import { Text } from "@/components/ui/text";
import { HorizontalPerfumeCard } from "../perfumes/HorizontalPerfumeCard";
import { SkeletonCard } from "./SkeletonResults";
import { LinearGradient } from "expo-linear-gradient";
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import React from "react";
import { useI18n } from "@/providers/i18n/i18n.provider";

interface ResultsHeaderSimilarProps {
  perfumeId: number;
}

export function ResultsHeaderSimilar({ perfumeId }: ResultsHeaderSimilarProps) {
  const router = useRouter();
  const { t } = useI18n();

  const { data: perfume, isLoading } = useSWR<components["schemas"]["Perfume"]>(
    `/perfumes/${perfumeId}`,
    { keepPreviousData: true },
  );

  const openPerfume = () => {
    router.push(`/(app)/(tabs)/(search)/search/perfume/${perfumeId}`);
  };

  return (
    <>
      <Text className="text-lg text-gold-ultralight mt-4 font-body-light-[16px] py-[16px]">
        {t("search.results.perfumeReference")}
      </Text>
      {isLoading ? (
        <SkeletonCard />
      ) : (
        <TouchableOpacity onPress={openPerfume} activeOpacity={0.6}>
          <LinearGradient
            colors={["#F1CA90", "#BF9E57"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{ padding: 1 }}
          >
            <HorizontalPerfumeCard perfume={perfume || {}} />
          </LinearGradient>
        </TouchableOpacity>
      )}
    </>
  );
}
