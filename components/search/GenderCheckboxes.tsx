import React, { <PERSON> } from "react";
import {
  Checkbox,
  CheckboxGroup,
  CheckboxIcon,
  CheckboxIndicator,
  CheckboxLabel,
} from "../ui/checkbox";
import { HStack } from "../ui/hstack";
import { VStack } from "../ui/vstack";
import { SectionTitle } from "./SectionTitle";
import { GENDER } from "@/constants/constants";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { createIcon, Svg } from "@gluestack-ui/icon";
import { Path } from "react-native-svg";

const CheckIcon = createIcon({
  Root: Svg,
  viewBox: '0 0 24 24',
  path: (
    <>
      <Path
        d="M20 6L9 17L4 12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </>
  ),
});

CheckIcon.displayName = 'CheckIcon';

export interface GenderCheckboxesProps {
  title: string;
  values: string[];
  onChange: (newValues: string[]) => void;
}

const GenderCheckboxes: FC<GenderCheckboxesProps> = ({
  title,
  values,
  onChange,
}) => {
  const constants = useTranslatedConstants();
  const { GENDER_LABELS } = constants;
  
  return (
    <VStack className="w-full py-3">
      <SectionTitle title={title} />
      <HStack className="bg-grey-card p-4 gap-4">
        <CheckboxGroup value={values} onChange={onChange}>
          <HStack space="xl">
            <Checkbox value={GENDER.HOMME}>
              <CheckboxIndicator className="rounded-none">
                <CheckboxIcon as={CheckIcon} />
              </CheckboxIndicator>
              <CheckboxLabel>{GENDER_LABELS.M}</CheckboxLabel>
            </Checkbox>
            <Checkbox value={GENDER.FEMME}>
              <CheckboxIndicator className="rounded-none">
                <CheckboxIcon as={CheckIcon} />
              </CheckboxIndicator>
              <CheckboxLabel>{GENDER_LABELS.F}</CheckboxLabel>
            </Checkbox>
            <Checkbox value={GENDER.ALL}>
              <CheckboxIndicator className="rounded-none">
                <CheckboxIcon as={CheckIcon} />
              </CheckboxIndicator>
              <CheckboxLabel>{GENDER_LABELS.all}</CheckboxLabel>
            </Checkbox>
          </HStack>
        </CheckboxGroup>
      </HStack>
    </VStack>
  );
};

export default GenderCheckboxes;
