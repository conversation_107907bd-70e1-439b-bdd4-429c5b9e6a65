import { ImageBackground } from "@/components/ui/image-background";
import { Text } from "@/components/ui/text";
import {
  Image,
  ImageSourcePropType,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
} from "react-native";

interface Props extends TouchableOpacityProps {
  displayName: string;
  backgroundSource: ImageSourcePropType;
  iconSource?: ImageSourcePropType;
  className?: string;
  style?: any;
  icon?: any;
}

export function SquareCardPopin({
  displayName,
  backgroundSource,
  iconSource,
  ...props
}: Props) {
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      {...props}
      className={`flex-1 h-[124px] ${props.className}`}
    >
      <ImageBackground
        source={backgroundSource}
        className="flex-1 items-center justify-center"
      >
        <View className="absolute inset-0 bg-black/60" />
        {iconSource ? (
          <Image source={iconSource} className="z-10" width={40} height={40} />
        ) : (
          props.icon && <props.icon width={40} height={40} />
        )}
        <Text
          style={{
            color: "#F2ECE0",
            fontSize: 16,
            fontWeight: "600",
            marginTop: 8,
            textAlign: "center",
          }}
        >
          {displayName}
        </Text>
      </ImageBackground>
    </TouchableOpacity>
  );
}
