import React, {
  useMemo,
  useRef,
  useState,
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useCallback,
} from "react";
import { View, FlatList, TouchableOpacity } from "react-native";
import useSWRInfinite from "swr/infinite";
import { useRouter } from "expo-router";
import { Text } from "@/components/ui/text";
import { SearchInput } from "@/components/search/SearchInput";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import AppButton from "../common/Button";
import { components } from "@/@types/api.types";
import { buildFilterQuery } from "@/utils/helpers";
import { SkeletonList } from "@/components/search/SkeletonResults";
import { BrandList } from "@/components/search/BrandList";
import { BrandCard } from "@/components/perfumes/BrandCard";
import { HorizontalPerfumeCard } from "@/components/perfumes/HorizontalPerfumeCard";
import { useDebounce, useDebouncedCallback } from "use-debounce";
import Divider from "../layout/Divider";
import { useI18n } from "@/providers/i18n/i18n.provider";
import logger from "@/utils/logger";

export default forwardRef((_props, ref) => {
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { TABS } = constants;
  const [searchQuery, setSearchQuery] = useState<string>("");
  const router = useRouter();
  const listRef = useRef<FlatList>(null);
  const [page, setPage] = useState(1);
  const [searchTerm] = useDebounce(searchQuery, 150);

  const [selectedBrand, setSelectedBrand] = useState<
    components["schemas"]["Brand"] | null
  >(null);

  const getKey = (pageIndex: number, previousPageData: any) => {
    if (previousPageData && !previousPageData?.data.length) return null;

    return `/brands${buildFilterQuery({
      page: pageIndex + 1,
      q: searchTerm.length >= 2 ? searchTerm : undefined,
      brand_id: selectedBrand?.id,
    })}`;
  };

  const { data, isLoading, isValidating, size, setSize } =
    useSWRInfinite(getKey);

  logger.info("SearchByBrand", {
    data,
  });

  const handleNextPage = useDebouncedCallback(() => {
    if (isLoading) return;
    setSize((size) => size + 1);
  }, 100);

  const items = useMemo(() => {
    if (!data) return [];

    // Create a Set to track unique IDs
    const uniqueIds = new Set();
    const uniqueItems = [];

    // Flatten the pages and filter out duplicates
    for (const page of data) {
      for (const item of page.data) {
        if (!uniqueIds.has(item.id)) {
          uniqueIds.add(item.id);
          uniqueItems.push(item);
        }
      }
    }

    return uniqueItems;
  }, [data]);

  const handleSearch = useCallback(
    (text: string) => {
      setSearchQuery(text.replace(/[\u2018\u2019\u201A]/g, "'"));
      setPage(1);
      if (listRef.current) {
        listRef.current.scrollToOffset({ animated: true, offset: -2 });
      }
    },
    [listRef]
  );

  const handleBrandSearch = useCallback(
    (brand: components["schemas"]["Brand"]) => {
      setSelectedBrand(brand);
      setSearchQuery("");
      setPage(1);
      if (listRef.current)
        listRef.current.scrollToOffset({ animated: true, offset: -2 });
    },
    [listRef]
  );

  useImperativeHandle(
    ref,
    () => ({
      handleReachEnd: () => {
        if (!selectedBrand && searchQuery.length === 0) return;
        handleNextPage();
      },
      handleBrandSearch: handleBrandSearch,
    }),
    [isLoading, size, selectedBrand, searchQuery, handleBrandSearch]
  );

  const resetBrandSearch = useCallback(() => {
    setSelectedBrand(null);
    setPage(1);
    if (listRef.current)
      listRef.current.scrollToOffset({ animated: true, offset: -2 });
  }, [listRef]);

  const handleItemPress = useCallback(
    (
      item: components["schemas"]["Perfume"] | components["schemas"]["Brand"]
    ) => {
      // @ts-ignore
      if (item.brandId) {
        router.push({
          pathname: `/(app)/(tabs)/(search)/search/results`,
          params: { perfume_id: item.id, tab: TABS.PERFUME },
        });
      } else {
        setSelectedBrand(item as components["schemas"]["Brand"]);
        setSearchQuery("");
        if (listRef.current)
          listRef.current.scrollToOffset({ animated: true, offset: -2 });
      }
    },
    [router, TABS.PERFUME, listRef]
  );

  return (
    <View className="flex-1 px-4 pb-16 mb-24">
      <Text
        className="text-lg text-gold-ultralight text-center leading-[22px] py-8"
        allowFontScaling={false}
      >
        {t("search.by_brand.description")}
      </Text>

      {selectedBrand ? (
        <>
          <BrandCard brand={selectedBrand} />
          <AppButton
            className="flex-1 border-gold-ultralight mt-4 mb-4"
            onPress={() => resetBrandSearch()}
          >
            {t("search.by_perfume.modify_selection")}
          </AppButton>
          <SearchInput
            searchQuery={searchQuery}
            handleSearch={handleSearch}
            placeholder={t("search.by_perfume.placeholder")}
          />
        </>
      ) : (
        <SearchInput
          searchQuery={searchQuery}
          handleSearch={handleSearch}
          placeholder={t("search.by_brand.placeholder")}
        />
      )}

      {isLoading && page === 1 && <SkeletonList count={10} />}
      {!isLoading && items.length === 0 && searchQuery && (
        <Text
          className="text-center text-gray-400 mt-4"
          allowFontScaling={false}
        >
          {t("search.by_brand.no_results")}
        </Text>
      )}

      {!isLoading &&
        items.length > 0 &&
        (selectedBrand || searchQuery.length !== 0) && (
          <>
            <Divider className="mb-4" />
            <FlatList
              ref={listRef}
              data={items}
              keyExtractor={(item) => item.id.toString()}
              scrollEnabled={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => handleItemPress(item)}
                  className="w-full mb-2"
                >
                  {item.brandId ? (
                    <HorizontalPerfumeCard
                      perfume={{
                        name: item.name,
                        brand: {
                          name:
                            item.brand?.display_name ||
                            item.brand?.name ||
                            t("search.by_perfume.unknown_brand"),
                        },
                        picture: { url: item.picture?.url || "" },
                        year: item.year,
                        type: item.type,
                        gender: item.gender,
                      }}
                    />
                  ) : (
                    <BrandCard brand={item} hideMarqueLabel={true} useGoldGradient={true} />
                  )}
                </TouchableOpacity>
              )}
              ListFooterComponent={
                isValidating ? <SkeletonList count={1} /> : undefined
              }
            />

            <Divider className="mt-2" />
          </>
        )}

      {!selectedBrand && searchQuery.length === 0 && (
        <BrandList handleSearch={handleBrandSearch} />
      )}
    </View>
  );
}); 