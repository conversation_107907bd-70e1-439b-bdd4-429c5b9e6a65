import React, {
  useEffect,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  useState,
  use<PERSON><PERSON>back,
} from "react";
import { View } from "react-native";
import {
  BOTTOM_SHEET_KEYS,
  SLIDER_PRICES,
  SLIDER_VOLUMES,
  getPerfumeTypesData,
} from "@/constants/constants";
import Divider from "../layout/Divider";
import { Text } from "@/components/ui/text";
import Selector from "./Selector";
import GradientButton from "../common/GradientButton";
import { HStack } from "../ui/hstack";
// import { ScrollView } from "react-native-gesture-handler";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { router } from "expo-router";
import StepSlider from "./StepSlider";
import SearchSelect from "./SearchSelect";
import { useDispatch } from "react-redux";
import GenderCheckboxes from "./GenderCheckboxes";
import { DEFAULT_FILTERS } from "@/models/search";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { Input, InputField } from "@/components/ui/input";
import { SectionTitle } from "@/components/search/SectionTitle";
import { Box } from "@/components/ui/box";
import { getNoteTranslationKey } from "@/utils/notes";

interface SearchByCriteriasProps {
  onOpenBottomSheet: (key: string) => void;
  onCloseBottomSheet: VoidFunction;
  family?: string;
}

export interface SearchByCriteriasRef {
  changeFilters: (
    id: string,
    data: any,
    shouldCloseBottomSheet?: boolean
  ) => void;
}

export interface Note {
  id: number;
  name: string;
}

export interface Family {
  id: string;
  name: string;
}

export interface Filters {
  family?: Family;
  note_top?: Note;
  note_middle?: Note;
  note_base?: Note;
  price_min?: number;
  price_max?: number;
  gender: string[];
  type: string;
  size_min?: number;
  size_max?: number;
  year?: number;
}

const SearchByCriterias = React.forwardRef<
  SearchByCriteriasRef,
  SearchByCriteriasProps
>(({ onOpenBottomSheet, onCloseBottomSheet, family }, ref) => {
  const dispatch = useDispatch();
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { TABS, FAMILIES } = constants;
  const [filters, setFilters] = useState<Filters>({
    ...DEFAULT_FILTERS,
  });

  const changeFilters = useCallback(
    (id: string, value: any, shouldCloseBottomSheet: boolean = false) => {
      setFilters((oldFilters) => ({ ...oldFilters, [id]: value }));
      if (shouldCloseBottomSheet) {
        onCloseBottomSheet();
      }
    },
    [onCloseBottomSheet]
  );

  useImperativeHandle(
    ref,
    () => ({
      changeFilters: (
        id: string,
        value: any,
        shouldCloseBottomSheet?: boolean
      ) => changeFilters(id, value, shouldCloseBottomSheet),
    }),
    [changeFilters]
  );

  const handleValidate = useCallback(() => {
    dispatch.search.addFilters(filters);
    router.push({
      pathname: `/(app)/(tabs)/criterias-results`,
      params: { tab: TABS.SUR_MESURE },
    });
  }, [dispatch, filters, router, TABS.SUR_MESURE]);

  useEffect(() => {
    if (family) {
      const selectedFamily = FAMILIES.find((fam) => fam.name === family);
      changeFilters(
        "family",
        selectedFamily
          ? { id: family, name: selectedFamily.displayName }
          : undefined,
        false
      );
    }
  }, [family, FAMILIES, changeFilters]);

  const handleOpenOlfactorySheet = useCallback(
    () => onOpenBottomSheet(BOTTOM_SHEET_KEYS.OLFACTORY),
    [onOpenBottomSheet]
  );
  const handleClearOlfactory = useCallback(
    () => changeFilters("family", undefined, true),
    [changeFilters]
  );

  const handleOpenTopNoteSheet = useCallback(
    () => onOpenBottomSheet(BOTTOM_SHEET_KEYS.TOP),
    [onOpenBottomSheet]
  );
  const handleClearTopNote = useCallback(
    () => changeFilters("note_top", undefined, true),
    [changeFilters]
  );

  const handleOpenHeartNoteSheet = useCallback(
    () => onOpenBottomSheet(BOTTOM_SHEET_KEYS.HEART),
    [onOpenBottomSheet]
  );
  const handleClearHeartNote = useCallback(
    () => changeFilters("note_middle", undefined, true),
    [changeFilters]
  );

  const handleOpenBaseNoteSheet = useCallback(
    () => onOpenBottomSheet(BOTTOM_SHEET_KEYS.BASE),
    [onOpenBottomSheet]
  );
  const handleClearBaseNote = useCallback(
    () => changeFilters("note_base", undefined, true),
    [changeFilters]
  );

  const handlePriceChange = useCallback(
    (value: number) =>
      changeFilters("price_max", value === 200 ? undefined : value),
    [changeFilters]
  );
  const handleGenderChange = useCallback(
    (selected: string[]) => changeFilters("gender", selected),
    [changeFilters]
  );
  const handleTypeChange = useCallback(
    (value: string) => changeFilters("type", value),
    [changeFilters]
  );
  const handleSizeChange = useCallback(
    (value: number) =>
      changeFilters("size_max", value === 200 ? undefined : value),
    [changeFilters]
  );
  const handleYearChange = useCallback(
    (value: string) => changeFilters("year", value),
    [changeFilters]
  );

  return (
    <View className="flex-1 px-4 pb-16">
      <Text
        className="text-lg text-gold-ultralight text-center leading-[22px] py-8"
        allowFontScaling={false}
      >
        {t("search.by_criterias.complete_profiles_text")}
      </Text>
      <Divider className="pb-2" />
      <Selector
        title={t("search.by_criterias.olfactory_family")}
        placeholder={t("search.by_criterias.select_olfactory_family")}
        value={filters.family?.name}
        onPress={handleOpenOlfactorySheet}
        onClear={handleClearOlfactory}
      />

      <Selector
        title={t("search.by_criterias.top_note")}
        placeholder={t("search.by_criterias.select_top_note")}
        value={
          filters.note_top?.name
            ? t(getNoteTranslationKey(filters.note_top?.name!)) ||
              filters.note_top?.name
            : undefined
        }
        onPress={handleOpenTopNoteSheet}
        onClear={handleClearTopNote}
      />

      <Selector
        title={t("search.by_criterias.heart_note")}
        value={
          filters.note_middle?.name
            ? t(getNoteTranslationKey(filters.note_middle?.name!)) ||
              filters.note_middle?.name
            : undefined
        }
        placeholder={t("search.by_criterias.select_heart_note")}
        onPress={handleOpenHeartNoteSheet}
        onClear={handleClearHeartNote}
      />

      <Selector
        title={t("search.by_criterias.base_note")}
        placeholder={t("search.by_criterias.select_base_note")}
        value={
          filters.note_base?.name
            ? t(getNoteTranslationKey(filters.note_base?.name!)) ||
              filters.note_base?.name
            : undefined
        }
        onPress={handleOpenBaseNoteSheet}
        onClear={handleClearBaseNote}
      />

      <StepSlider
        title={t("search.by_criterias.price")}
        data={SLIDER_PRICES}
        unit="€"
        onChange={handlePriceChange}
      />

      <GenderCheckboxes
        title={t("search.by_criterias.gender")}
        values={filters.gender}
        onChange={handleGenderChange}
      />

      <SearchSelect
        title={t("search.by_criterias.perfume_category")}
        placeholder={t("search.by_criterias.select_perfume_category")}
        data={getPerfumeTypesData()}
        value={filters.type}
        onChange={handleTypeChange}
      />

      <StepSlider
        title={t("search.by_criterias.size")}
        data={SLIDER_VOLUMES}
        unit="ml"
        onChange={handleSizeChange}
      />

      <Box className="mt-2" />
      <SectionTitle title={t("search.by_criterias.year")} />
      <Input variant="outline" size="lg" className="rounded-none">
        <InputField
          placeholder={t("search.by_criterias.year")}
          onChangeText={handleYearChange}
        />
      </Input>

      <Divider className="py-4" />

      <HStack className="mt-6 pb-20">
        <GradientButton className="w-full" onPress={handleValidate}>
          {t("search.by_criterias.confirm_selection")}
        </GradientButton>
      </HStack>
    </View>
  );
});

SearchByCriterias.displayName = "SearchByCriterias";
export default SearchByCriterias;
