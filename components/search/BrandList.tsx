import useSWR from "swr";
import { components } from "@/@types/api.types";
import { memo, useCallback, useMemo, useState, useEffect, useRef } from "react";
import { SectionList } from "@/components/ui/section-list";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { TouchableOpacity, InteractionManager, Animated } from "react-native";
import { Card } from "@/components/ui/card";
import { HStack } from "@/components/ui/hstack";
import { View } from "react-native";
import { Image } from "@/components/ui/image";

// Function to get the character image path
const getCharacterImagePath = (letter: string): any => {
  const charImages: { [key: string]: any } = {
    '0': require('@/assets/images/chars/0.png'),
    '1': require('@/assets/images/chars/1.png'),
    '2': require('@/assets/images/chars/2.png'),
    '3': require('@/assets/images/chars/3.png'),
    '4': require('@/assets/images/chars/4.png'),
    '5': require('@/assets/images/chars/5.png'),
    '6': require('@/assets/images/chars/6.png'),
    '7': require('@/assets/images/chars/7.png'),
    '8': require('@/assets/images/chars/8.png'),
    '9': require('@/assets/images/chars/9.png'),
    'A': require('@/assets/images/chars/a.png'),
    'B': require('@/assets/images/chars/b.png'),
    'C': require('@/assets/images/chars/c.png'),
    'D': require('@/assets/images/chars/d.png'),
    'E': require('@/assets/images/chars/e.png'),
    'F': require('@/assets/images/chars/f.png'),
    'H': require('@/assets/images/chars/h.png'),
    'I': require('@/assets/images/chars/i.png'),
    'J': require('@/assets/images/chars/j.png'),
    'K': require('@/assets/images/chars/k.png'),
    'L': require('@/assets/images/chars/l.png'),
    'M': require('@/assets/images/chars/m.png'),
    'N': require('@/assets/images/chars/n.png'),
    'O': require('@/assets/images/chars/o.png'),
    'P': require('@/assets/images/chars/p.png'),
    'Q': require('@/assets/images/chars/q.png'),
    'R': require('@/assets/images/chars/r.png'),
    'S': require('@/assets/images/chars/s.png'),
    'T': require('@/assets/images/chars/t.png'),
    'U': require('@/assets/images/chars/u.png'),
    'V': require('@/assets/images/chars/v.png'),
    'W': require('@/assets/images/chars/w.png'),
    'X': require('@/assets/images/chars/x.png'),
    'Y': require('@/assets/images/chars/y.png'),
    'Z': require('@/assets/images/chars/z.png'),
  };
  
  return charImages[letter.toUpperCase()] || null;
};
import { backAssetsUrl } from "@/utils/domain";
import { VStack } from "@/components/ui/vstack";
import { Spinner } from "@/components/ui/spinner";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useRouter } from "expo-router";

// Utility function to get the alphabetical letter for grouping
const getAlphabeticalLetter = (brandName: string): string => {
  if (!brandName) return '#';
  
  let name = brandName.trim();
  
  // Handle dot prefix - if name starts with ".", use the next character
  if (name.startsWith('.') && name.length > 1) {
    name = name.substring(1);
  }
  
  // Get the first character
  let firstChar = name[0];
  
  // Normalize French characters (remove accents)
  const frenchCharMap: { [key: string]: string } = {
    'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a',
    'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
    'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
    'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o',
    'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
    'ç': 'c', 'ñ': 'n',
    'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A',
    'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E',
    'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I',
    'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O',
    'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U',
    'Ç': 'C', 'Ñ': 'N'
  };
  
  // Normalize the character if it's a French accented character
  if (frenchCharMap[firstChar]) {
    firstChar = frenchCharMap[firstChar];
  }
  
  // Convert to uppercase and check if it's a letter or number
  const upperChar = firstChar.toUpperCase();
  
  // Return the letter if it's A-Z, return the number if it's 0-9, otherwise return '#' for symbols
  if (/^[A-Z]$/.test(upperChar)) {
    return upperChar;
  } else if (/^[0-9]$/.test(firstChar)) {
    return firstChar;
  } else {
    return '#';
  }
};

type BrandListArray = {
  letter: string;
  data: components["schemas"]["Brand"][];
}[];

const NUM_COLUMNS = 2;
const CARD_HEIGHT = 74; // Fixed height for brand cards
const ROW_HEIGHT = CARD_HEIGHT + 16; // Card height + vertical margins

export function BrandList({
  handleSearch,
}: {
  handleSearch?: (brand: components["schemas"]["Brand"]) => void;
}) {
  const { t } = useI18n();

  const { data: brands, isLoading } =
    useSWR<components["schemas"]["Brand"][]>("/brands?all=1");

  // State for deferred data processing
  const [brandByLetter, setBrandByLetter] = useState<BrandListArray>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Process brands data after interactions complete
  useEffect(() => {
    if (!brands || brands.length === 0) {
      setBrandByLetter([]);
      return;
    }

    setIsProcessing(true);
    
    // Defer heavy processing to avoid blocking UI
    InteractionManager.runAfterInteractions(() => {
      const letterMap = new Map<string, components["schemas"]["Brand"][]>();
      
      // Process in chunks to avoid blocking
      const processChunk = (startIdx: number) => {
        const chunkSize = 50;
        const endIdx = Math.min(startIdx + chunkSize, brands.length);
        
        for (let i = startIdx; i < endIdx; i++) {
          const brand = brands[i];
          const letter = getAlphabeticalLetter(brand.name || '');
          if (!letterMap.has(letter)) {
            letterMap.set(letter, []);
          }
          letterMap.get(letter)!.push(brand);
        }
        
        if (endIdx < brands.length) {
          // Process next chunk in next frame
          requestAnimationFrame(() => processChunk(endIdx));
        } else {
          // All chunks processed, update state
          const result = Array.from(letterMap.entries())
            .map(([letter, data]) => ({ 
              letter, 
              data: data.sort((a, b) => (a.name || '').localeCompare(b.name || '')) 
            }))
            .sort((a, b) => {
              // Custom sorting: numbers (0-9) first, then letters (A-Z), then symbols (#) at the end
              const isANumber = /^[0-9]$/.test(a.letter);
              const isBNumber = /^[0-9]$/.test(b.letter);
              const isALetter = /^[A-Z]$/.test(a.letter);
              const isBLetter = /^[A-Z]$/.test(b.letter);
              
              // Numbers come first
              if (isANumber && !isBNumber) return -1;
              if (!isANumber && isBNumber) return 1;
              
              // If both are numbers, sort numerically
              if (isANumber && isBNumber) {
                return parseInt(a.letter) - parseInt(b.letter);
              }
              
              // Letters come after numbers
              if (isALetter && !isBLetter && b.letter !== '#') return -1;
              if (!isALetter && isBLetter && a.letter !== '#') return 1;
              
              // If both are letters, sort alphabetically
              if (isALetter && isBLetter) {
                return a.letter.localeCompare(b.letter);
              }
              
              // Symbols (#) come last
              if (a.letter === '#' && b.letter !== '#') return 1;
              if (a.letter !== '#' && b.letter === '#') return -1;
              
              return a.letter.localeCompare(b.letter);
            });
          
          setBrandByLetter(result);
          setIsProcessing(false);
        }
      };
      
      processChunk(0);
    });
  }, [brands]);

  const groupData = useCallback((data: components["schemas"]["Brand"][]) => {
    const rows = [];
    for (let i = 0; i < data.length; i += NUM_COLUMNS) {
      rows.push(data.slice(i, i + NUM_COLUMNS));
    }
    return rows;
  }, []);

  // Prepare sections data for SectionList with lazy processing
  const [sectionsData, setSectionsData] = useState<any[]>([]);
  
  useEffect(() => {
    if (brandByLetter.length === 0) {
      setSectionsData([]);
      return;
    }
    
    // Process sections after interactions
    InteractionManager.runAfterInteractions(() => {
      const sections = brandByLetter.map((section) => ({
        letter: section.letter,
        data: groupData(section.data),
      }));
      setSectionsData(sections);
    });
  }, [brandByLetter, groupData]);

  if (isLoading || isProcessing) {
    // Show skeleton loading with proper dimensions and dark theme
    return (
      <View className="mb-4">
        {[...Array(5)].map((_, sectionIndex) => (
          <View key={`skeleton-section-${sectionIndex}`}>
            {/* Section header skeleton - dark theme */}
            <View className="my-4">
              <View className="h-[32px] w-16 rounded" style={{ backgroundColor: '#1a1a1a' }} />
            </View>
            {[...Array(3)].map((_, rowIndex) => (
              <View 
                key={`skeleton-row-${sectionIndex}-${rowIndex}`}
                className="flex-row gap-2" 
                style={{ height: ROW_HEIGHT }}
              >
                {[...Array(NUM_COLUMNS)].map((_, colIndex) => (
                  <View
                    key={`skeleton-item-${sectionIndex}-${rowIndex}-${colIndex}`}
                    className="flex-1"
                    style={{ 
                      marginRight: colIndex === 0 && NUM_COLUMNS > 1 ? 8 : 0,
                      height: CARD_HEIGHT 
                    }}
                  >
                    <Card size="sm" className="flex-1 rounded-none h-full" style={{ backgroundColor: '#1a1a1a' }}>
                      <HStack className="items-center h-full py-3">
                        {/* Logo skeleton */}
                        <View className="w-[50px] h-[50px] rounded" style={{ backgroundColor: '#2a2a2a' }} />
                        {/* Text skeleton */}
                        <View className="flex-1 ml-2 justify-center" style={{ height: 50 }}>
                          <View>
                            <View className="h-4 rounded mb-2" style={{ backgroundColor: '#2a2a2a', width: '80%' }} />
                            <View className="h-4 rounded" style={{ backgroundColor: '#2a2a2a', width: '60%' }} />
                          </View>
                        </View>
                      </HStack>
                    </Card>
                  </View>
                ))}
              </View>
            ))}
          </View>
        ))}
      </View>
    );
  }

  if (!isLoading && !isProcessing && brandByLetter.length === 0) {
    return (
      <VStack className="flex-1 justify-center items-center py-8">
        <Text>{t("search.by_perfume.no_brands_available")}</Text>
      </VStack>
    );
  }

  // Always use SectionList for better performance
  return (
    <SectionList
      className="mb-4"
      sections={sectionsData}
      keyExtractor={(item, index) => `row-${index}-${item[0]?.id}`}
      renderItem={({ item }) => (
        <View className="flex-row gap-2" style={{ height: ROW_HEIGHT }}>
          {item.map((brand: components["schemas"]["Brand"], index: number) => (
            <View
              key={brand.id}
              className="flex-1"
              style={{ 
                marginRight: index === 0 && item.length > 1 ? 8 : 0,
                height: CARD_HEIGHT 
              }}
            >
              <BrandLine brand={brand} onPress={handleSearch} />
            </View>
          ))}
          {item.length === 1 && (
            <View className="flex-1" style={{ height: CARD_HEIGHT }} />
          )}
        </View>
      )}
      renderSectionHeader={({ section: { letter } }) => {
        const charImage = getCharacterImagePath(letter);
        return (
          <View className="mt-8 mb-6">
            {charImage ? (
              <Image
                source={charImage}
                alt={letter}
                className="w-12 h-12"
                resizeMode="contain"
              />
            ) : (
              <Heading className="text-gold-ultralight heading-bold text-[24px]">
                {letter}
              </Heading>
            )}
          </View>
        );
      }}
      // Performance optimizations
      stickySectionHeadersEnabled={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={5}
      updateCellsBatchingPeriod={100}
      initialNumToRender={10}
      windowSize={5}
      // Disable scrolling if nested in another ScrollView
      scrollEnabled={false}
      nestedScrollEnabled={false}
      // Lazy rendering
      onEndReachedThreshold={0.5}
    />
  );
}

// Optimized BrandLine component with lazy image loading
const BrandLine = memo(
  ({
    brand,
    onPress,
  }: {
    brand: components["schemas"]["Brand"];
    onPress?: (brand: components["schemas"]["Brand"]) => void;
  }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);
    const router = useRouter();

    const handlePress = useCallback(() => {
      if (onPress) {
        onPress(brand);
      } else {
        // Navigate to brand details page
        router.push(`/(app)/(tabs)/(search)/search/brand/${brand.id}`);
      }
    }, [onPress, brand, router]);

    const handleImageError = useCallback(() => {
      setImageError(true);
    }, []);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
    }, []);

    const brandInitial = useMemo(() => {
      const name = brand.display_name || brand.name || "?";
      // Handle dot prefix for display
      if (name.startsWith('.') && name.length > 1) {
        return name[1].toUpperCase();
      }
      return name[0].toUpperCase();
    }, [brand.display_name, brand.name]);
    
    const brandName = useMemo(
      () => (brand.display_name || brand.name || "").toUpperCase(),
      [brand.display_name, brand.name]
    );

    return (
      <TouchableOpacity activeOpacity={0.6} onPress={handlePress} style={{ height: '100%' }}>
        <Card size="sm" className="flex-1 rounded-none h-full">
          <HStack className="items-center h-full py-3">
            <View className="w-[50px] h-[50px] flex-none">
              {brand.logo?.url && !imageError ? (
                <>
                  {!imageLoaded && (
                    <View className="w-[50px] h-[50px] bg-gray-200 absolute" />
                  )}
                  <Image
                    source={{ uri: backAssetsUrl(brand.logo.url) }}
                    alt={brand.name}
                    className="w-[50px] h-[50px]"
                    onError={handleImageError}
                    onLoad={handleImageLoad}
                    resizeMode="contain"
                  />
                </>
              ) : (
                <View className="w-[50px] h-[50px] bg-white items-center justify-center">
                  <Text className="text-black text-2xl">{brandInitial}</Text>
                </View>
              )}
            </View>
            <View className="flex-1 ml-2 justify-center" style={{ height: 50 }}>
              <Text
                numberOfLines={2}
                ellipsizeMode="tail"
                className="text-md text-grey-200"
                style={{ 
                  lineHeight: 20,
                  textAlignVertical: 'center'
                }}
              >
                {brandName}
              </Text>
            </View>
          </HStack>
        </Card>
      </TouchableOpacity>
    );
  },
);