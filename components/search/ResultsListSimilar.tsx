import { Text } from "@/components/ui/text";
import { SkeletonList } from "./SkeletonResults";
import Divider from "../layout/Divider";
import { Card } from "../ui/card";
import { FlatList, TouchableOpacity } from "react-native";
import { HorizontalPerfumeCard } from "../perfumes/HorizontalPerfumeCard";
import { useRouter } from "expo-router";
import { components } from "@/@types/api.types";
import useSWR from "swr";
import React from "react";
import { useI18n } from "@/providers/i18n/i18n.provider";

interface ResultsListSimilarProps {
  perfumeId: number;
  price: {
    label: string;
    min: number;
    max: number | null;
  } | null;
}

export function ResultsListSimilar({
  perfumeId,
  price,
}: ResultsListSimilarProps) {
  const router = useRouter();
  const { t } = useI18n();

  const { data: perfumes, isLoading: isLoadingSimilar } = useSWR<
    {
      perfume: components["schemas"]["Perfume"];
      similarity: number;
    }[]
  >(
    `/perfumes/${perfumeId}/similar?${
      price !== null
        ? `price_min=${Number(price?.min)}&price_max=${Number(price?.max)}`
        : ""
    }`
  );

  const onItemPress = (id: number) => {
    router.push(`/(app)/(tabs)/(search)/search/perfume/${id}`);
  };
  return (
    <>
      <Text className="text-lg text-gold-ultralight mt-10 mb-2 font-body-light-[16px] ">
        {t("search.idealPerfume")} ({perfumes ? perfumes.length : "..."})
      </Text>

      <Divider className="my-4 mt-2" />

      {isLoadingSimilar ? (
        <SkeletonList />
      ) : (
        <>
          <FlatList
            data={perfumes}
            keyExtractor={(item) => item.perfume.id!.toString()}
            scrollEnabled={false}
            ListEmptyComponent={() => (
              <Card className="rounded-none">
                <Text className="text-center">
                  {t("search.noIdealPerfume")}
                </Text>
              </Card>
            )}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => onItemPress(item.perfume.id!)}
                className="w-full mb-2"
              >
                <HorizontalPerfumeCard
                  perfume={item.perfume}
                  percentage={Number((item.similarity * 100).toFixed(0))}
                />
              </TouchableOpacity>
            )}
          />
        </>
      )}
    </>
  );
}
