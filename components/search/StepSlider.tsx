import { AwesomeSliderP<PERSON>, Slider } from "react-native-awesome-slider";
import { StyleSheet, View } from "react-native";
import { useSharedValue } from "react-native-reanimated";
import React from "react";
import { SectionTitle } from "./SectionTitle";
import { VStack } from "../ui/vstack";
import { HStack } from "../ui/hstack";
import { Text } from "../ui/text";

export interface AppInputProps extends Partial<AwesomeSliderProps> {
  title: string;
  containerClassName?: string;
  data: string[];
  min?: number;
  max?: number;
  unit?: string;
  onChange: (value: number) => void;
}

export default function StepSlider({
  title,
  containerClassName,
  data,
  min = 0,
  max = 200,
  unit,
  onChange,
  ...props
}: AppInputProps) {
  const progress = useSharedValue(100);
  const minimumValue = useSharedValue(min);
  const maximumValue = useSharedValue(max);
  const thumbScaleValue = useSharedValue(1);

  return (
    <VStack className="w-full py-3">
      <SectionTitle title={title} />
      <View className={`bg-grey-card p-2 ${containerClassName}`}>
        <Slider
          theme={{
            minimumTrackTintColor: "#D9B575",
            bubbleBackgroundColor: "#D9B575",
          }}
          bubble={
            unit
              ? (value) => (value > 150 ? "+" : `${value}${unit}`)
              : undefined
          }
          onSlidingComplete={onChange}
          markStyle={{
            width: 4,
            height: 7,
            borderRadius: 25,
          }}
          style={styles.slider}
          forceSnapToStep
          steps={4}
          thumbWidth={18}
          progress={progress}
          minimumValue={minimumValue}
          maximumValue={maximumValue}
          thumbScaleValue={thumbScaleValue}
          {...props}
        />
        <HStack className="justify-between">
          {data.map((value, index) => (
            <Text key={index} className="text-xs text-gold-ultralight">
              {value}
            </Text>
          ))}
        </HStack>
      </View>
    </VStack>
  );
}

const styles = StyleSheet.create({
  slider: {
    marginBottom: 14,
    marginTop: 12,
  },
});
