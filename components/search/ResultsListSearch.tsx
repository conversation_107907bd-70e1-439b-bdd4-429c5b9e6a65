import React from "react";
import {
  View,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { Text } from "@/components/ui/text";
import { components } from "@/@types/api.types";
import { HorizontalPerfumeCard } from "@/components/perfumes/HorizontalPerfumeCard";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useRouter } from "expo-router";
import useSWR from "swr";

interface ResultsListSearchProps {
  filters?: string;
  perfumes?: {
    data: components["schemas"]["Perfume"][];
    meta: {
      total: number;
      page: number;
      last_page: number;
    };
  };
  loading?: boolean;
  onEndReached?: () => void;
}

export function ResultsListSearch({
  filters,
  perfumes: propsPerfumes,
  loading: propsLoading,
  onEndReached,
}: ResultsListSearchProps) {
  const { t } = useI18n();
  const router = useRouter();

  const { data: fetchedPerfumes, isLoading } = useSWR(
    filters ? `/perfumes${filters}` : null,
    { keepPreviousData: true },
  );

  const perfumes = propsPerfumes || fetchedPerfumes;
  const loading = propsLoading || isLoading;

  const onItemPress = (id: number) => {
    router.push(`/(app)/(tabs)/(search)/search/perfume/${id}`);
  };

  return (
    <View className="flex-1">
      <Text className="text-gold-ultralight text-lg font-bold mb-4">
        {t("search.idealPerfume")} ({perfumes ? perfumes.data.length : 0})
      </Text>

      {loading && !perfumes && (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#BF9E57" />
        </View>
      )}

      {perfumes && perfumes.data.length === 0 && (
        <View className="flex-1 justify-center items-center">
          <Text className="text-gold-ultralight text-center">
            {t("search.noIdealPerfume")}
          </Text>
        </View>
      )}

      {perfumes && perfumes.data.length > 0 && (
        <FlatList
          data={perfumes.data}
          keyExtractor={(item) => item.id?.toString() || ""}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => item.id && onItemPress(item.id)}
              className="w-full mb-2"
            >
              <HorizontalPerfumeCard perfume={item} />
            </TouchableOpacity>
          )}
          onEndReached={onEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            loading ? (
              <View className="py-4">
                <ActivityIndicator size="small" color="#BF9E57" />
              </View>
            ) : null
          }
        />
      )}
    </View>
  );
}
