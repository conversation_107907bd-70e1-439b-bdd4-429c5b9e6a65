import { View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { CriteriaCard } from "./CriteriaCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

interface ResultsHeaderSearchProps {
  filters: any;
  isSimplified?: boolean;
}

export function ResultsHeaderSearch({ filters, isSimplified = false }: ResultsHeaderSearchProps) {
  const { t } = useI18n();
  
  if (isSimplified) {
    return (
      <View className="flex-1 mt-6">
        <LinearGradient
          colors={["#F1CA90", "#BF9E57"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ padding: 1 }}
        >
          <View className="bg-background-dark p-2 rounded-none">
            <View className="flex-row">
              <View className="flex-1">
                <CriteriaCard
                  cardTitle={t("search.by_criterias.inspiration_olfactive_1")}
                  title={
                    // @ts-ignore
                    filters.note_olfactive_1?.name ?? t("search.by_criterias.none")
                  }
                />
              </View>
              <View className="flex-1 pl-2">
                <CriteriaCard
                  cardTitle={t("search.by_criterias.inspiration_olfactive_2")}
                  title={
                    // @ts-ignore
                    filters.note_olfactive_2?.name ?? t("search.by_criterias.none")
                  }
                />
              </View>
            </View>
            <View className="flex-row mt-2">
              <View className="flex-1">
                <CriteriaCard
                  cardTitle={t("search.by_criterias.inspiration_olfactive_3")}
                  title={
                    // @ts-ignore
                    filters.note_olfactive_3?.name ?? t("search.by_criterias.none")
                  }
                />
              </View>
              <View className="flex-1 pl-2">
                {/* Empty space to maintain layout */}
              </View>
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View className="flex-1 mt-6">
      <LinearGradient
        colors={["#F1CA90", "#BF9E57"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ padding: 1 }}
      >
        <View className="bg-background-dark p-2 rounded-none">
          <View className="flex-row">
            <View className="flex-1">
              <CriteriaCard
                cardTitle={t("search.by_criterias.olfactory_family")}
                title={
                  // @ts-ignore
                  filters.family?.name ?? t("search.by_criterias.none")
                }
              />
            </View>
            <View className="flex-1 pl-2">
              <CriteriaCard
                cardTitle={t("search.by_criterias.top_note")}
                title={
                  // @ts-ignore
                  filters.note_top?.name ?? t("search.by_criterias.none")
                }
              />
            </View>
          </View>
          <View className="flex-row mt-2">
            <View className="flex-1">
              <CriteriaCard
                cardTitle={t("search.by_criterias.base_note")}
                title={
                  // @ts-ignore
                  filters.note_base?.name ?? t("search.by_criterias.none")
                }
              />
            </View>
            <View className="flex-1 pl-2">
              <CriteriaCard
                cardTitle={t("search.by_criterias.heart_note")}
                title={
                  // @ts-ignore
                  filters.note_middle?.name ??
                  t("search.by_criterias.none")
                }
              />
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}
