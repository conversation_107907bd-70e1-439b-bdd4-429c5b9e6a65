import React, {
  useState,
  useMemo,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
  useCallback,
} from "react";
import BottomSheet, {
  BottomSheetFlatList,
  BottomSheetModal,
  BottomSheetProps,
  BottomSheetScrollView,
} from "@gorhom/bottom-sheet";
import Divider from "../layout/Divider";
import { SquareCardPopin } from "./SquareCardPopin";
import { Heading } from "../ui/heading";
import { VStack } from "../ui/vstack";
import { SearchInput } from "./SearchInput";
import { Text } from "../ui/text";
import { useDebounce } from "use-debounce";
import { Card } from "@/components/ui/card";
import { Spinner } from "../ui/spinner";
import useSWR from "swr";
import GradientButton from "../common/GradientButton";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { getNoteTranslationKey } from "@/utils/notes";

export interface BottomSheetRef {
  openModal: VoidFunction;
  closeModal: VoidFunction;
}

interface Props extends Partial<BottomSheetProps> {
  id: string;
  title: string;
  description: string;
  placeholder: string;
  existingData?: any[];
  onPress: (id: string, data: any) => void;
}

const CustomBottomSheet = forwardRef<BottomSheetRef, Props>(
  (
    { id, title, description, placeholder, existingData, onPress, ...props },
    ref
  ) => {
    const sheetRef = useRef<BottomSheetModal>(null);
    const [searchQuery, setSearchQuery] = useState("");
    const [q] = useDebounce(searchQuery, 300);
    const [page, setPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [allData, setAllData] = useState<any[]>([]);
    const { t } = useI18n();
    const constants = useTranslatedConstants();
    const { FAMILIES } = constants;

    // Create a stable swrKey that doesn't change on every render
    const swrKey = useMemo(
      () =>
        !existingData
          ? `/notes?per_page=10&page=${page}${q ? `&q=${q}` : ""}`
          : null,
      [existingData, page, q]
    );

    const { data, isLoading } = useSWR(swrKey, {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 5000, // Prevent duplicate requests within 5 seconds
      shouldRetryOnError: false,
    });

    // Handle data changes when search or initial load
    useEffect(() => {
      if (data && data.meta) {
        setHasMore(page < data.meta.lastPage);

        // Reset data when search changes or on first page
        if (page === 1) {
          setAllData(data.data);
        } else if (page > 1 && isLoadingMore) {
          // Append data for pagination
          setAllData((prevData) => [...prevData, ...data.data]);
          setIsLoadingMore(false);
        }
      }
    }, [data, page, isLoadingMore]);

    // Reset page when search changes
    useEffect(() => {
      if (page !== 1) {
        setPage(1);
      }
    }, [q]);

    useImperativeHandle(ref, () => ({
      openModal: () => openModal(),
      closeModal: () => closeModal(),
    }));

    const closeModal = () => {
      sheetRef.current?.close();
    };

    const openModal = () => {
      sheetRef.current?.snapToIndex(1);
    };

    const handleSearch = useCallback((query: string) => {
      setSearchQuery(query);
    }, []);

    const loadMore = useCallback(() => {
      if (!isLoadingMore && !isLoading && hasMore) {
        setIsLoadingMore(true);
        setPage((prevPage) => prevPage + 1);
      }
    }, [isLoadingMore, isLoading, hasMore]);

    const staticData: any[] = useMemo(() => {
      if (existingData && q) {
        return existingData.filter((item) =>
          item.displayName.toLowerCase().includes(q.toLowerCase())
        );
      } else if (existingData) {
        return existingData;
      }
      return [];
    }, [q, existingData]);

    // Determine current data to display
    const displayData = existingData ? staticData : allData;

    return (
      <BottomSheet
        ref={sheetRef}
        snapPoints={["75%", "90%"]}
        index={-1}
        enablePanDownToClose={true}
        onClose={() => setSearchQuery("")}
        backgroundStyle={{
          backgroundColor: "#171717",
          borderRadius: 16,
        }}
        handleIndicatorStyle={{
          backgroundColor: "#D1D1D1",
          width: 50,
        }}
        {...props}
      >
        <>
          <VStack className="top-0 p-4">
            <Heading className="text-gold-ultralight font-heading-bold text-2xl">
              {title}
            </Heading>
            <Text className="text-gold-ultralight my-4 text-base">
              {description}
            </Text>

            <SearchInput
              searchQuery={searchQuery}
              handleSearch={handleSearch}
              placeholder={placeholder}
            />
          </VStack>
          <BottomSheetScrollView>
            {isLoading && !isLoadingMore && <Spinner color="black" />}
            <BottomSheetFlatList
              className="bg-background-dark min-w-full"
              ListHeaderComponent={<Divider className="py-2" />}
              data={displayData}
              keyExtractor={(item, index) =>
                (item.id || item.name || index).toString()
              }
              numColumns={2}
              scrollEnabled={false}
              ListEmptyComponent={() => (
                <Card className="rounded-none">
                  <Text className="text-center">
                    {isLoading
                      ? t("search.loading")
                      : t("search.no_results", { query: q })}
                  </Text>
                </Card>
              )}
              renderItem={({ item, index }) => {
                const icon = item.icon;
                const iconSource = item.icon?.url
                  ? { uri: process.env.EXPO_PUBLIC_API_URL + item.icon.url }
                  : undefined;
                const cover = item.cover
                  ? {
                      uri: process.env.EXPO_PUBLIC_API_URL + item.cover.url,
                    }
                  : (item.backgroundSource ??
                    (item.id
                      ? {
                          uri: `${process.env.EXPO_PUBLIC_API_URL}/storage/uploads/notes/${item.id}.webp`,
                        }
                      : null) ??
                    FAMILIES[3].backgroundSource);

                // Get translated name for notes
                const displayName =
                  id === "family"
                    ? item.displayName
                    : t(getNoteTranslationKey(item.name!)) ||
                      item.displayName ||
                      item.name;

                return (
                  <SquareCardPopin
                    key={index}
                    displayName={displayName}
                    icon={icon}
                    iconSource={iconSource}
                    backgroundSource={cover}
                    style={{ margin: 5 }}
                    onPress={() =>
                      onPress(id, {
                        //{id, name} for notes / {name, displayname} for family
                        id: item.id || item.name,
                        name: item.displayName || item.name,
                      })
                    }
                  />
                );
              }}
              ListFooterComponent={
                id !== "family" && hasMore ? (
                  <VStack className="mt-2 px-2">
                    <GradientButton
                      onPress={loadMore}
                      disabled={isLoading || isLoadingMore || !hasMore}
                    >
                      {isLoadingMore
                        ? t("search.loading")
                        : t("search.see_more")}
                    </GradientButton>
                    <Divider className="py-2" />
                  </VStack>
                ) : (
                  <Divider className="py-2" />
                )
              }
              showsVerticalScrollIndicator={false}
              contentContainerClassName="pb-[150px]"
            />
          </BottomSheetScrollView>
        </>
      </BottomSheet>
    );
  }
);

CustomBottomSheet.displayName = "CustomBottomSheet";
export default CustomBottomSheet;
