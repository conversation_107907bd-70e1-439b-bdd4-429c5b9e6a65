import { Card } from "../ui/card";
import { VStack } from "../ui/vstack";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";

type CriteriaCardProps = {
  cardTitle: string;
  title: string;
  imageSource?: string;
};

export const CriteriaCard = ({
  cardTitle,
  title,
  imageSource,
}: CriteriaCardProps) => {
  return (
    <Card
      className={`bg-grey-card items-center justify-center rounded-none p-4`}
    >
      <VStack className="relative items-center">
        <Image
          size="lg"
          source={
            imageSource
              ? { uri: imageSource }
              : require("@/assets/images/perfume-families/bg-floral.jpg")
          }
          alt={title}
          className="w-20 h-20 rounded-full grayscale mb-4"
        />
        <Text className="text-body-light text-grey-400 text-center text-[14px]">
          {cardTitle}
        </Text>
        <Heading className=" text-body-semibold text-[16px] text-gold-ultralight ">
          {title}
        </Heading>
      </VStack>
    </Card>
  );
};
