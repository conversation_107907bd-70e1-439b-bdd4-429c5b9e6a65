import { TextInput, View, TouchableOpacity } from "react-native";
import SearchIcon from "@/assets/images/icons/search.svg";
import ClearIcon from "@/assets/images/icons/clear.svg";

type Props = {
  searchQuery: string;
  handleSearch: (text: string) => void;
  placeholder?: string;
};

export function SearchInput({ searchQuery, handleSearch, placeholder }: Props) {
  return (
    <View className="flex-row items-center py-4 px-2 bg-[#323232] mb-4">
      <View style={{ marginRight: 8 }} className="text-gold-ultralight">
        <SearchIcon width={24} height={24} />
      </View>

      <TextInput
        placeholder={placeholder || "Rechercher"}
        placeholderTextColor="#A2A2A2"
        style={{
          flex: 1,
          color: "#A2A2A2",
          fontSize: 14,
          fontWeight: "300",
          fontFamily: "body-light",
        }}
        allowFontScaling={false}
        value={searchQuery}
        onChangeText={handleSearch}
      />

      {searchQuery.length > 0 && (
        <TouchableOpacity
          onPress={() => handleSearch("")}
          className="text-gold-ultralight mr-1"
          style={{ paddingHorizontal: 10 }}
          hitSlop={{ top: 14, bottom: 14, left: 14, right: 14 }}
        >
          <ClearIcon width={12} height={12} />
        </TouchableOpacity>
      )}
    </View>
  );
}
