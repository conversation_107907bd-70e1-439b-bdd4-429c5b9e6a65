import { TouchableOpacity } from "react-native";
import ArrowDownIcon from "@/assets/images/icons/chevron_down.svg";
import { VStack } from "../ui/vstack";
import { SectionTitle } from "./SectionTitle";
import { Text } from "../ui/text";
import { HStack } from "../ui/hstack";
import ClearIcon from "@/assets/images/icons/clear.svg";

interface SelectorProps {
  title: string;
  placeholder: string;
  value?: string | number | null;
  onPress: (e: any) => any;
  onClear?: () => void;
}

export default function Selector({
  title,
  placeholder,
  value,
  onPress,
  onClear,
}: SelectorProps) {
  return (
    <VStack className="w-full py-3 relative">
      <SectionTitle title={title} />
      <TouchableOpacity
        onPress={onPress}
        className="border border-grey-400 bg-grey-card"
      >
        <HStack className="justify-between p-4">
          <Text
            className={
              Boolean(value)
                ? "body-semibold text-gold-ultralight"
                : "text-grey-600"
            }
            allowFontScaling={false}
          >
            {value || placeholder}
          </Text>
          <ArrowDownIcon width={20} height={20} stroke="#F2ECE0" />
        </HStack>
      </TouchableOpacity>

      {Boolean(value) && (
        <VStack className="absolute top-[74%] right-[15%]">
          <TouchableOpacity
            onPress={() => onClear?.()}
            activeOpacity={0.7}
            className="text-gold-ultralight"
          >
            <ClearIcon width={12} height={12} />
          </TouchableOpacity>
        </VStack>
      )}
    </VStack>
  );
}
