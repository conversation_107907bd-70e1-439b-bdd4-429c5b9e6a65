import { Animated, useWindowDimensions } from "react-native";
import { HStack } from "@/components/ui/hstack";

type Props = {
  scrollX: Animated.Value;
  length: number;
};

export function HorizontalScrollDots({ scrollX, length }: Props) {
  const { width: windowWidth } = useWindowDimensions();

  return (
    <HStack className="justify-center items-center h-[8px]">
      {[...Array(Math.floor(length)).keys()].map((_perfume, perfumeIndex) => {
        const backgroundColor = scrollX.interpolate({
          inputRange: [
            windowWidth * (perfumeIndex - 1),
            windowWidth * perfumeIndex,
            windowWidth * (perfumeIndex + 1),
          ],
          outputRange: [
            "rgba(162, 162, 162, 1)",
            "rgba(0,0,0,0)",
            "rgba(162, 162, 162, 1)",
          ],
          extrapolate: "clamp",
        });

        const borderWidth = scrollX.interpolate({
          inputRange: [
            windowWidth * (perfumeIndex - 1),
            windowWidth * perfumeIndex,
            windowWidth * (perfumeIndex + 1),
          ],
          outputRange: [0, 1, 0],
          extrapolate: "clamp",
        });

        const dotSize = scrollX.interpolate({
          inputRange: [
            windowWidth * (perfumeIndex - 1),
            windowWidth * perfumeIndex,
            windowWidth * (perfumeIndex + 1),
          ],
          outputRange: [8, 12, 8],
          extrapolate: "clamp",
        });

        const borderRadius = scrollX.interpolate({
          inputRange: [
            windowWidth * (perfumeIndex - 1),
            windowWidth * perfumeIndex,
            windowWidth * (perfumeIndex + 1),
          ],
          outputRange: [4, 6, 4],
          extrapolate: "clamp",
        });

        return (
          <Animated.View
            key={perfumeIndex}
            style={[
              {
                marginHorizontal: 4,
                borderColor: "rgba(162, 162, 162, 1)",
              },
              {
                backgroundColor,
                borderWidth,
                borderRadius,
                height: dotSize,
                width: dotSize,
              },
            ]}
          />
        );
      })}
    </HStack>
  );
}
