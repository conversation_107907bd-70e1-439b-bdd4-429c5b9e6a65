import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { FC } from "react";
import { SvgProps } from "react-native-svg";
import { View } from "react-native";

type Props = {
  step: number;
  title: string;
  description: string;
  icon?: FC<SvgProps>;
  style?: any;
};

const NUMBER_ROMAN = {
  1: "I",
  2: "II",
  3: "III",
  4: "IV",
};

export function InfoCard({
  step,
  title,
  description,
  icon: Icon,
  style,
}: Props) {
  return (
    <Card
      className="bg-grey-card relative rounded-none"
      style={style}
      size="sm"
    >
      {Icon && (
        <View className="absolute right-0 top-0">
          <Icon />
        </View>
      )}
      <Heading className="text-gold-ultralight mt-4 text-[64px] heading-regular-italic">
        {/* @ts-ignore */}
        {NUMBER_ROMAN[step] || ""}
      </Heading>
      <Heading className="text-gold-ultralight text-2xl mt-1 font-heading-semibold">
        {title}
      </Heading>
      <Text className="text-grey-500 text-[14px] mt-6">{description}</Text>
    </Card>
  );
}
