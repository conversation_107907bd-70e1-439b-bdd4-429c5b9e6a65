import { Dimensions, View } from "react-native";
import { Heading } from "@/components/ui/heading";
import { HStack } from "@/components/ui/hstack";
import StarIcon from "@/assets/images/icons/star-point.svg";
import StarWarsBackground from "@/assets/images/headers/star-wars.svg";
import { VStack } from "@/components/ui/vstack";

type Props = {
  altText?: string;
  title: string;
};

export function HeaderTitle({ title }: Props) {
  const screenWidth = Dimensions.get("window").width;

  return (
    <VStack className="relative w-full h-[128px] items-center justify-center">
      <View className="absolute top-0 left-0 -z-0">
        <StarWarsBackground width={screenWidth} height={128} />
      </View>

      <HStack space="xl" className="items-center">
        <StarIcon color="#F2ECE0" />
        <Heading className="text-gold-ultralight" size="xl">
          {title}
        </Heading>
        <StarIcon color="#F2ECE0" />
      </HStack>
    </VStack>
  );
}
