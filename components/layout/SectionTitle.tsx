import { Heading } from "@/components/ui/heading";
import { HStack } from "@/components/ui/hstack";
import Star from "@/assets/images/icons/star-point.svg";

type Props = {
  title: string;
};

export function SectionTitle({ title }: Props) {
  return (
    <HStack className="items-center gap-2 py-2.5 px-4 border-t-grey-600 border-b-grey-600 border-t border-b">
      <Star color="#F2ECE0" />
      <Heading className="text-gold-ultralight" size="lg">
        {title}
      </Heading>
    </HStack>
  );
}
