import { ImageBackground } from "@/components/ui/image-background";
import { Dimensions } from "react-native";
import GradientButton from "@/components/common/GradientButton";
import { useRouter } from "expo-router";
import { HStack } from "../ui/hstack";
import { useMemo } from "react";
import { useI18n } from "@/providers/i18n/i18n.provider";

const BACKGROUND_IMAGE_FR = require("@/assets/images/home/<USER>");
const BACKGROUND_IMAGE_EN = require("@/assets/images/home/<USER>");

export function HomeSection() {
  const router = useRouter();
  const screenHeight = Dimensions.get("window").height;
  const { t, locale } = useI18n();

  const source = useMemo(() => {
    if (locale.includes("fr")) {
      return BACKGROUND_IMAGE_FR;
    }
    return BACKGROUND_IMAGE_EN;
  }, [locale]);

  return (
    <ImageBackground
      source={source}
      style={{
        height: screenHeight - 190,
      }}
    >
      <HStack className="absolute bottom-24 px-7">
        <GradientButton
          className="w-full"
          onPress={() => router.push("/(app)/(tabs)/(search)/search")}
        >
          {t("home.find_ideal_perfume_button")}
        </GradientButton>
      </HStack>
    </ImageBackground>
  );
}
