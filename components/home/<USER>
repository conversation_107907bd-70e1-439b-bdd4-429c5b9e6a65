import { VStack } from "@/components/ui/vstack";
import { SectionTitle } from "@/components/layout/SectionTitle";
import { ImageBackground } from "@/components/ui/image-background";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { TouchableOpacity, View } from "react-native";

import ArrowRightIcon from "@/assets/images/icons/arrow-right.svg";
import { useRouter } from "expo-router";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { useI18n } from "@/providers/i18n/i18n.provider";

export function OlfactiveFamilySection() {
  const router = useRouter();
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { TABS, FAMILIES } = constants;
  
  const openFamily = (family: string) => {
    router.push(
      `/(app)/(tabs)/criterias?family=${family}`,
    );
  };

  return (
    <VStack>
      <SectionTitle title={t("home.perfumes_by_family")} />

      <VStack space="sm" className="p-4 mt-4 mb-2">
        {FAMILIES.map((family) => (
          <TouchableOpacity
            key={family.name}
            activeOpacity={0.6}
            onPress={() => openFamily(family.name)}
          >
            <ImageBackground source={family.backgroundSource}>
              <View className="absolute inset-0 bg-black/65" />
              <HStack className="p-4 items-center justify-between">
                <HStack className="gap-2 items-center">
                  <family.icon />
                  <Text className="font-body-semibold text-gold-ultralight text-lg">
                    {family.displayName}
                  </Text>
                </HStack>
                <ArrowRightIcon />
              </HStack>
            </ImageBackground>
          </TouchableOpacity>
        ))}
      </VStack>
    </VStack>
  );
}
