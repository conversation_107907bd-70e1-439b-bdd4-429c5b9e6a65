import { VStack } from "@/components/ui/vstack";
import { SectionTitle } from "@/components/layout/SectionTitle";

import CarreauIcon from "@/assets/images/icons/carreau.svg";
import CoeurIcon from "@/assets/images/icons/coeur.svg";
import PiqueIcon from "@/assets/images/icons/pique.svg";
import TrefleIcon from "@/assets/images/icons/trefle.svg";

import { Animated, Dimensions } from "react-native";
import React, { useRef } from "react";
import { HorizontalScrollDots } from "@/components/layout/HorizontalScrollDots";
import GradientButton from "@/components/common/GradientButton";
import { FlatList } from "react-native-gesture-handler";
import * as Linking from "expo-linking";
import { InfoCard } from "@/components/layout/InfoCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

export function AboutSection() {
  const { t } = useI18n();
  const screenWidth = Dimensions.get("window").width;
  const scrollX = useRef(new Animated.Value(0)).current;

  const steps = [
    {
      title: t("home.about_step_1_title"),
      description: t("home.about_step_1_description"),
      icon: TrefleIcon,
    },
    {
      title: t("home.about_step_2_title"),
      description: t("home.about_step_2_description"),
      icon: CoeurIcon,
    },
    {
      title: t("home.about_step_3_title"),
      description: t("home.about_step_3_description"),
      icon: PiqueIcon,
    },
    {
      title: t("home.about_step_4_title"),
      description: t("home.about_step_4_description"),
      icon: CarreauIcon,
    },
  ];

  return (
    <VStack space="4xl">
      <SectionTitle title={t("home.about_section_title")} />

      <VStack space="2xl" className="px-4">
        <FlatList
          data={steps}
          renderItem={({ item, index }) => (
            <InfoCard
              key={index}
              step={index + 1}
              title={item.title}
              description={item.description}
              icon={item.icon}
              style={{
                width: screenWidth - 32,
              }}
            />
          )}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
        />
        <VStack className="mb-8">
          <HorizontalScrollDots scrollX={scrollX} length={steps.length} />
        </VStack>
      </VStack>
      <VStack className="mb-4 px-4">
        <GradientButton
          onPress={() =>
            Linking.openURL("mailto:<EMAIL>").catch((err) =>
              console.error("An error occurred", err),
            )
          }
        >
          {t("home.about_contact_button")}
        </GradientButton>
      </VStack>
    </VStack>
  );
}
