import { VStack } from "@/components/ui/vstack";
import { SectionTitle } from "@/components/layout/SectionTitle";
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { Animated, Dimensions, TouchableOpacity } from "react-native";
import { useRef } from "react";
import { HorizontalScrollDots } from "@/components/layout/HorizontalScrollDots";
import { useRouter } from "expo-router";
import { FlatList } from "react-native-gesture-handler";
import { BasicPerfumeCard } from "../perfumes/BasicPerfumeCard";
import { useI18n } from "@/providers/i18n/i18n.provider";

export function FeaturedPerfumesSection() {
  const router = useRouter();
  const { t } = useI18n();
  const { data: perfumes } = useSWR<{
    data: components["schemas"]["Perfume"][];
  }>("/perfumes?is_featured=true", { keepPreviousData: false });

  const screenWidth = Dimensions.get("window").width;
  const scrollX = useRef(new Animated.Value(0)).current;

  const openPerfume = (perfumeId: number) => {
    router.push(`/(app)/(tabs)/(home)/search/perfume/${perfumeId}`);
  };

  return (
    <VStack space="4xl" className="mt-6">
      <SectionTitle title={t("home.iconic_perfumes_section_title")} />

      <VStack space="2xl" className="mb-8 px-0">
        <FlatList
          data={perfumes?.data}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              key={index}
              activeOpacity={0.6}
              onPress={() => openPerfume(item.id!)}
            >
              <BasicPerfumeCard
                perfume={item}
                style={{
                  width: screenWidth / 2 - 16,
                  marginHorizontal: 8,
                }}
              />
            </TouchableOpacity>
          )}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
        />

        {perfumes?.data && perfumes?.data?.length > 1 && (
          <HorizontalScrollDots
            scrollX={scrollX}
            length={perfumes.data.length / 2}
          />
        )}
      </VStack>
    </VStack>
  );
}
