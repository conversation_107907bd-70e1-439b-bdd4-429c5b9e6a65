import { VStack } from "@/components/ui/vstack";
import { SectionTitle } from "@/components/layout/SectionTitle";

import ClockIcon from "@/assets/images/perfume-infos/clock.svg";
import StarIcon from "@/assets/images/perfume-infos/star.svg";
import FlowerIcon from "@/assets/images/perfume-infos/flower.svg";
import { Animated, Dimensions } from "react-native";
import { InfoCard } from "@/components/layout/InfoCard";
import React, { useRef, useMemo } from "react";
import { HorizontalScrollDots } from "@/components/layout/HorizontalScrollDots";
import { FlatList } from "react-native-gesture-handler";
import GradientButton from "@/components/common/GradientButton";
import { useRouter } from "expo-router";
import { useI18n } from "@/providers/i18n/i18n.provider";
export function PerfumeInfosSection() {
  const screenWidth = Dimensions.get("window").width;
  const scrollX = useRef(new Animated.Value(0)).current;
  const { t } = useI18n();
  const router = useRouter();

  const steps = useMemo(() => [
    {
      title: t("home.step_1_title"),
      description: t("home.step_1_description"),
      icon: ClockIcon,
    },
    {
      title: t("home.step_2_title"), // tête
      description: t("home.step_2_description"),
      icon: StarIcon,
    },
    {
      title: t("home.step_4_title"), // cœur
      description: t("home.step_4_description"),
      icon: ClockIcon,
    },
    {
      title: t("home.step_3_title"), // fond
      description: t("home.step_3_description"),
      icon: FlowerIcon,
    },
  ], [t]);

  return (
    <VStack space="4xl">
      <SectionTitle title={t("home.section_title")} />

      <VStack space="2xl" className="px-4">
        <FlatList
          data={steps}
          renderItem={({ item, index }) => (
            <InfoCard
              key={index}
              step={index + 1}
              title={item.title}
              description={item.description}
              icon={item.icon}
              style={{
                width: screenWidth - 32,
              }}
            />
          )}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
        />
        <VStack className="mb-8">
          <HorizontalScrollDots scrollX={scrollX} length={steps.length} />
        </VStack>
        <VStack className="mb-4 px-4">
          <GradientButton
            onPress={() =>
              router.push(`/(app)/(tabs)/criterias`)
            }
          >
            {t("home.button_text")}
          </GradientButton>
        </VStack>
      </VStack>
    </VStack>
  );
}
