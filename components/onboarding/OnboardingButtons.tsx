import React from "react";
import { View, Text, Pressable } from "react-native";
import GradientButton from "@/components/common/GradientButton";
import { router } from "expo-router";
import { useI18n } from "@/providers/i18n/i18n.provider";
import AppButton from "@/components/common/Button";
import { HStack } from "@/components/ui/hstack";

export default function OnboardingButtons() {
  const { t } = useI18n();
  
  return (
    <View className="absolute bottom-16 w-full px-8">
      <HStack className="z-10 gap-4">
        <AppButton className="flex-1" onPress={() => router.push("/register")}>
          {t("auth.register")}
        </AppButton>
        <GradientButton
          className="flex-1"
          onPress={() => router.push("/sign-in")}
        >
          {t("auth.login")}
        </GradientButton>
      </HStack>
      <Pressable onPress={() => router.push("/home")}>
        <Text className="mt-4 text-center text-gray-500 underline">
          {t("auth.register_later")}
        </Text>
      </Pressable>
    </View>
  );
}
