import { OnboardingElement } from "@/app/onboarding";
import { FC } from "react";
import { useWindowDimensions, ImageBackground } from "react-native";

interface OnboardingItemProps {
  item: OnboardingElement;
}

const OnboardingItem: FC<OnboardingItemProps> = ({ item }) => {
  const { width } = useWindowDimensions();
  return (
    <ImageBackground
      source={item.image}
      resizeMode="cover"
      className="flex-1"
      style={{ width }}
    />
  );
};

export default OnboardingItem;
