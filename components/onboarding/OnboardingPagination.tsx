import React, { <PERSON> } from "react";
import { Animated } from "react-native";
import { HStack } from "@/components/ui/hstack";

interface OnboardingPaginationProps {
  data: any[];
  currentIndex: number;
}

const OnboardingPagination: FC<OnboardingPaginationProps> = ({
  data,
  currentIndex,
}) => {
  return (
    <HStack className="z-10 absolute top-16 gap-2 px-5">
      {data.map((_, i) => (
        <Animated.View
          key={i}
          className={`flex-1 h-[2px] ${currentIndex >= i ? "bg-gold-200" : "bg-grey-600"}`}
        />
      ))}
    </HStack>
  );
};

export default OnboardingPagination;
