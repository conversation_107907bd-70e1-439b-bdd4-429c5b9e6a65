import React, { createContext, useContext, useState, ReactNode } from 'react';
import {
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { useI18n } from '@/providers/i18n/i18n.provider';
import GradientButton from '@/components/common/GradientButton';

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface AlertOptions {
  cancelable?: boolean;
  onDismiss?: () => void;
}

interface AlertConfig {
  title?: string;
  message?: string;
  buttons?: AlertButton[];
  options?: AlertOptions;
}

interface AlertContextType {
  showAlert: (config: AlertConfig) => void;
  hideAlert: () => void;
  t: (key: string) => string;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

// Global reference to the alert methods
let alertRef: AlertContextType | null = null;

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

export const AlertProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [config, setConfig] = useState<AlertConfig>({});
  const { t } = useI18n();

  const showAlert = (alertConfig: AlertConfig) => {
    setConfig(alertConfig);
    setIsVisible(true);
  };

  const hideAlert = () => {
    setIsVisible(false);
    if (config.options?.onDismiss) {
      config.options.onDismiss();
    }
  };

  const handleButtonPress = (button: AlertButton) => {
    if (button.onPress) {
      button.onPress();
    }
    hideAlert();
  };

  const handleBackdropPress = () => {
    if (config.options?.cancelable !== false) {
      hideAlert();
    }
  };


  const contextValue: AlertContextType = {
    showAlert,
    hideAlert,
    t,
  };

  // Set the global reference when provider mounts
  React.useEffect(() => {
    alertRef = contextValue;
    return () => {
      alertRef = null;
    };
  }, [contextValue]);

  return (
    <AlertContext.Provider value={contextValue}>
      {children}
      <AlertDialog isOpen={isVisible} onClose={hideAlert}>
        <AlertDialogBackdrop
          onPress={handleBackdropPress}
        />
        <AlertDialogContent className="rounded-none">
          {config.title && (
            <AlertDialogHeader className="justify-center">
              <Heading size="lg" className="text-center">
                {config.title}
              </Heading>
            </AlertDialogHeader>
          )}

          {config.message && (
            <AlertDialogBody className="mt-3 mb-4">
              <Text size="md" className="text-center">
                {config.message}
              </Text>
            </AlertDialogBody>
          )}

          <AlertDialogFooter className="flex-col gap-2">
            {config.buttons?.map((button, index) => (
              <GradientButton
                key={index}
                className="w-full"
                onPress={() => handleButtonPress(button)}
              >
                {button.text}
              </GradientButton>
            ))}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AlertContext.Provider>
  );
};

// Static API similar to React Native's Alert.alert
export const Alert = {
  alert: (
    title?: string,
    message?: string,
    buttons?: AlertButton[],
    options?: AlertOptions
  ) => {
    if (!alertRef) {
      console.warn('CustomAlert provider not found. Make sure to wrap your app with AlertProvider.');
      return;
    }

    // Default to single "OK" button if no buttons provided
    const defaultButtons: AlertButton[] = buttons?.length ? buttons : [
      { text: alertRef.t('common.ok') || 'OK', style: 'default' }
    ];

    alertRef.showAlert({
      title,
      message,
      buttons: defaultButtons,
      options,
    });
  }
};

export type { AlertButton, AlertOptions };