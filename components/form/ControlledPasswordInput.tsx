import React, { <PERSON> } from "react";
import { Controller } from "react-hook-form";
import AppPasswordInput, {
  AppPasswordInputProps,
} from "../common/PasswordInput";

interface ControlledPasswordInputProps extends AppPasswordInputProps {
  name: string;
  control: any;
  title: string;
  placeholder: string;
}

const ControlledPasswordInput: FC<ControlledPasswordInputProps> = ({
  name,
  control,
  title,
  placeholder,
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onBlur, onChange },
        fieldState,
        formState,
      }) => (
        <AppPasswordInput
          title={title}
          placeholder={placeholder}
          value={value}
          onChangeText={onChange}
          onBlur={onBlur}
          {...formState}
          {...fieldState}
          {...props}
        />
      )}
    />
  );
};

export default ControlledPasswordInput;
