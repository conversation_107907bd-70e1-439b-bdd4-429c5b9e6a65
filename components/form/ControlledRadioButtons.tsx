import React, { <PERSON> } from "react";
import { Controller } from "react-hook-form";
import AppRadioButtons, { AppRadioButtonsProps } from "../common/RadioButtons";

interface ControlledRadioButtonsProps extends AppRadioButtonsProps {
  name: string;
  control: any;
  title: string;
  data: any[];
  onValidate?: (name: string, value: string) => Promise<void>;
}

const ControlledRadioButtons: FC<ControlledRadioButtonsProps> = ({
  name,
  control,
  title,
  data,
  onValidate,
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onBlur, onChange },
        fieldState,
        formState,
      }) => {
        return (
          <AppRadioButtons
            data={data}
            title={title}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            onValidate={onValidate ? () => onValidate(name, value) : undefined}
            {...formState}
            {...fieldState}
            {...props}
          />
        );
      }}
    />
  );
};

export default ControlledRadioButtons;
