import React, { FC } from "react";
import { Controller } from "react-hook-form";
import AppDateInput, { AppDateInputProps } from "../common/DateInput";
import { format, parse } from "date-fns";

interface ControlledDateInputProps extends Partial<AppDateInputProps> {
  name: string;
  control: any;
  title: string;
  placeholder: string;
  onValidate?: (name: string, value: string) => Promise<void>;
}

const ControlledDateInput: FC<ControlledDateInputProps> = ({
  name,
  control,
  title,
  placeholder,
  onValidate,
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onBlur, onChange },
        fieldState,
        formState,
      }) => {
        return (
          <AppDateInput
            title={title}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            onValidate={
              onValidate
                ? () =>
                    onValidate(
                      name,
                      format(
                        parse(value, "dd/MM/yyyy", new Date()),
                        "yyyy-MM-dd",
                      ),
                    )
                : undefined
            }
            {...formState}
            {...fieldState}
            {...props}
          />
        );
      }}
    />
  );
};

export default ControlledDateInput;
