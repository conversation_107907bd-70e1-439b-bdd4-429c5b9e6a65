import React, { <PERSON> } from "react";
import { Controller } from "react-hook-form";
import AppInput, { AppInputProps } from "../common/Input";

interface ControlledInputProps extends AppInputProps {
  name: string;
  control: any;
  title: string;
  placeholder: string;
  onValidate?: (name: string, value: string) => Promise<void>;
}

const ControlledInput: FC<ControlledInputProps> = ({
  name,
  control,
  title,
  placeholder,
  onValidate,
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onBlur, onChange },
        fieldState,
        formState,
      }) => {
        return (
          <AppInput
            title={title}
            placeholder={placeholder}
            error={fieldState.error}
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            onValidate={onValidate ? () => onValidate(name, value) : undefined}
            {...formState}
            {...fieldState}
            {...props}
          />
        );
      }}
    />
  );
};

export default ControlledInput;
