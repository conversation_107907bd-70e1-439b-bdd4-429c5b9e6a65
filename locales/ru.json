{"layout": {"home": "Главная", "search": "Поиск", "favorites": "Избранное", "profile": "Профиль"}, "common": {"ok": "OK", "alert": "Предупреждение", "save": "Сохранить", "edit": "Редактировать", "cancel": "Отмена", "delete": "Удалить", "genders": {"female": "Женский", "male": "Мужской", "mixed": "Унисекс"}, "families": {"hesperides": "Цитрусовые", "floral": "Цветочные", "woody": "Древесные", "fern": "Папоротниковые", "chypre": "Шипровые", "oriental": "Восточные", "aromatic": "Ароматические"}}, "auth": {"register": "Регистрация", "login": "Вход", "register_later": "Зарегистрироваться позже", "email_address": "Электронная почта", "password": "Пароль", "email_placeholder": "пример@email.com", "password_placeholder": "Создать пароль", "forgot_password": "Забыли пароль?", "login_button": "Войти", "reset_password": "Сбросить пароль", "back_to_login": "Вернуться к входу", "reset_email_sent": "Ссылка для сброса пароля отправлена на вашу почту!", "gender": "Пол", "username": "Имя пользователя *", "birthdate": "Дата рождения", "country": "Страна", "city": "Город", "phone": "Номер телефона", "next_step": "Следующий шаг", "register_button": "Создать аккаунт", "register_success": "Ваш аккаунт создан, письмо с подтверждением отправлено на ваш адрес!", "password_confirmation": "Подтверждение пароля", "gender_placeholder": "Выберите ваш пол", "username_placeholder": "Введите ваше имя пользователя", "birthdate_placeholder": "ДД/ММ/ГГГГ", "country_placeholder": "Введите вашу страну (напр. RU)", "city_placeholder": "Введите ваш город", "phone_placeholder": "Введите ваш номер телефона", "password_confirmation_placeholder": "Подтвердите ваш пароль", "errors": {"gender_required": "Необходимо указать пол.", "username_required": "Необходимо указать имя пользователя.", "phone_invalid_format": "Формат должен быть: 0601020304.", "country_invalid_format": "Страна должна быть в формате ISO, напр. RU.", "city_required": "Необходимо указать город.", "password_confirmation_required": "Необходимо подтвердить пароль.", "passwords_mismatch": "Пароли не совпадают.", "invalid_credentials": "Неверные учетные данные, попробуйте снова.", "email_required": "Необходимо указать электронную почту.", "email_invalid": "Неверный формат электронной почты.", "password_required": "Необходимо указать пароль.", "reset_email_error": "Ошибка при отправке письма для сброса пароля, попробуйте снова.", "register_error": "Ошибка при создании аккаунта."}}, "home": {"find_ideal_perfume_button": "Ищу свой идеальный аромат", "iconic_perfumes_section_title": "Культовые ароматы", "featured_brands_section_title": "Культовые бренды момента", "perfumes_by_family": "Ароматы по ольфакторным семействам", "section_title": "Выберите свой аромат в 4 шага", "step_1_title": "Найдите ольфакторное семейство", "step_1_description": "Ольфакторное семейство классифицирует ароматы по их основным ольфакторным свойствам и описывает стиль и доминирующий характер аромата.", "step_2_title": "Выберите верхнюю ноту", "step_2_description": "Верхняя нота - это первое впечатление от аромата, запах, который появляется сразу после нанесения. Обычно она легкая, свежая и летучая, длится от нескольких минут до часа.", "step_3_title": "Выберите базовую ноту", "step_3_description": "Базовая нота - это аромат, который держится на коже дольше всего, часто несколько часов после нанесения. Она придает аромату глубину и стойкость и обычно состоит из богатых и устойчивых запахов.", "step_4_title": "Выберите сердечную ноту", "step_4_description": "Сердечная нота - это аромат, который появляется после исчезновения верхних нот. Она формирует сердце аромата и длится несколько часов, придавая ему основной характер. Сердечные ноты обычно цветочные, пряные или фруктовые.", "button_text": "Хочу персональный поиск", "about_section_title": "Вместе,\nмы исследуем мир арома<PERSON>ов", "about_step_1_title": "Гармония ароматов", "about_step_1_description": "Когда два аромата имеют сходство 80% или более, их ноты сливаются настолько гармонично, что могут восприниматься как идентичные запахи, создавая впечатление одного аромата. Однако каждый аромат сохраняет свою подпись и индивидуальность. Эта тонкость придает каждому запаху свою красоту.", "about_step_2_title": "Ваш опыт вознаграждается", "about_step_2_description": "Помогите нам улучшить приложение! Если вы заметили, что два аромата похожи, но еще не в списке, свяжитесь с нами. Если это сходство подтвердится, мы предложим вам награду в благодарность за ваш вклад.", "about_step_3_title": "Внимание ценится", "about_step_3_description": "Если вы заметили ошибку в составе аромата или его обработке, сообщите нам. В благодарность за ваше внимание мы отправим вам эксклюзивный сюрприз.", "about_step_4_title": "Открытия ценятся", "about_step_4_description": "Если вы знаете аромат или бренд, которого еще нет в нашем приложении, сообщите нам. В благодарность за вашу вовлеченность мы отправим вам особое признание.", "about_contact_button": "Связаться с нами"}, "perfume": {"composition_title": "Композиция", "head_notes_title": "Верхние ноты", "head_notes_info": "Верхние ноты - это первое впечатление от аромата, запах, который появляется сразу после нанесения. Обычно они легкие, свежие и летучие, исчезают через несколько минут до часа.", "heart_notes_title": "Сердечные ноты", "heart_notes_info": "Сердечные ноты появляются после исчезновения верхних нот и формируют сердце аромата. Они длятся несколько часов и придают запаху его основной характер. Сердечные ноты обычно цветочные, пряные или фруктовые.", "base_notes_title": "Базовые ноты", "base_notes_info": "Базовые ноты - это самые стойкие запахи на коже, часто длящиеся несколько часов после нанесения. Они придают аромату глубину и стойкость и обычно состоят из богатых и устойчивых запахов.", "olfactory_profile_title": "Ольфакторный профиль", "olactive_variations": "Ольфакторные вариации", "price_comparison": "Сравнение цен", "default_price": "Лучшая цена", "mother_house": "Материнский дом", "show_more": "Показать больше", "show_less": "Показать меньше", "compare_prices": "Сравнить цены", "buy_now": "Купить сейчас", "redirecting": "Перенаправление...", "register_to_see_prices": "Зарегистрируйтесь, чтобы увидеть цены", "most_wanted_perfumes": "Самые желаемые ароматы"}, "brand": {"visit_website": "Посетить сайт", "official_website": "Официал<PERSON>ный сайт", "visit_official_website": "Посетите официальный сайт бренда", "most_wanted_brands": "Самые желаемые бренды"}, "search": {"search_by_perfume": "По аромату", "search_by_brand": "По бренду", "search_by_criterias": "Персональный", "perfume": "Аромат", "perfume_extract": "Экстракт аромата", "see_more": "Показать больше", "loading": "Загрузка...", "no_results": "Нет результатов для поиска: \"{{query}}\"", "idealPerfume": "Идеальный аромат", "noIdealPerfume": "Нет идеального аромата, соответствующего вашим критериям", "results": {"searchResults": "Результаты вашего поиска", "modifySelection": "Изменить выбор", "perfumeReference": "Референс аромата"}, "by_perfume": {"description": "Введите ваш любимый аромат и позвольте направить вас к тем, которые подходят вам лучше всего.", "modify_selection": "Изменить выбор", "placeholder": "Введите аромат или бренд", "no_results": "Результаты не найдены.", "unknown_brand": "Неизвестный бренд", "no_brands_available": "В данный момент нет доступных брендов"}, "by_brand": {"description": "Откройте для себя бренды и изучите их полные коллекции ароматов.", "placeholder": "Введите название бренда", "no_results": "Бренды не найдены."}, "by_criterias": {"complete_profiles_text": "Заполните четыре ольфакторных профиля ниже, чтобы открыть аромат, который идеально вам подходит", "olfactory_family": "Ольфакторное семейство", "select_olfactory_family": "Выберите ольфакторную категорию", "top_note": "Верхняя нота", "select_top_note": "Выберите верхнюю ноту", "heart_note": "Сердечная нота", "select_heart_note": "Выберите сердечную ноту", "base_note": "Базовая нота", "select_base_note": "Выберите базовую ноту", "price": "Цена", "gender": "Пол", "perfume_category": "Категория аромата", "select_perfume_category": "<PERSON><PERSON> de parfum, <PERSON><PERSON> de toilette...", "size": "Размер", "confirm_selection": "Подтвердить мой выбор", "olfactory_family_description": "Ольфакторное семейство группирует ароматы по их основным свойствам, предлагая тонкую классификацию их запахов.", "search_olfactory_family": "Поиск ольфакторной категории", "top_note_description": "Верхние ноты, на вершине ольфакторной пирамиды, дают первое впечатление от аромата.", "search_top_note": "Поиск верхней ноты", "base_note_description": "Это самая плотная и интенсивная часть ольфакторной пирамиды. Они придают аромату глубину.", "search_base_note": "Поиск базовой ноты", "heart_note_description": "Сердечная нота - это сердце аромата, которое появляется после верхних нот и придает ему основной характер.", "search_heart_note": "Поиск сердечной ноты", "note_olfactive_1": "Обонятельная Нота 1", "select_note_olfactive_1": "Выбрать Обонятельную Ноту 1", "note_olfactive_2": "Обонятельная Нота 2", "select_note_olfactive_2": "Выбрать Обонятельную Ноту 2", "note_olfactive_3": "Обонятельная Нота 3", "select_note_olfactive_3": "Выбрать Обонятельную Ноту 3", "inspiration_olfactive_1": "Обонятельное Вдохновение 1", "select_inspiration_olfactive_1": "Выбрать Обонятельное Вдохновение 1", "inspiration_olfactive_2": "Обонятельное Вдохновение 2", "select_inspiration_olfactive_2": "Выбрать Обонятельное Вдохновение 2", "inspiration_olfactive_3": "Обонятельное Вдохновение 3", "select_inspiration_olfactive_3": "Выбрать Обонятельное Вдохновение 3", "none": "Нет", "year": "Год создания"}}, "favorites": {"yourFavorites": "Ваше избранное", "noFavorites": "У вас пока нет избранных ароматов. Вернитесь позже!", "errors": {"not_authenticated": "Вы должны войти в систему для выполнения этого действия."}}, "personalNotes": {"addNote": "Добавить личную заметку", "personalNote": "Личная Заметка", "placeholder": "Напишите свои мысли об этом парфюме..."}, "profile": {"hello": "Здравствуйте", "unauthenticated": "Не авторизован", "unauthenticated_infos": "Вы должны войти в систему для доступа к этим функциям.", "version": "Версия", "edit_profile": "Редактировать профиль", "logout": "Выйти", "delete_account": "Удалить аккаунт", "confirm_delete_title": "Вы уверены, что хотите удалить свой аккаунт?", "confirm_delete_description": "Это действие необратимо и приведет к удалению вашего аккаунта и всех связанных данных. Подтвердите, если хотите продолжить.", "update_success": "Вы обновили свой профиль!", "logout_success": "Вы вышли из системы", "account_delete_success": "Ваш аккаунт успешно удален", "privacy_policy": "Политика конфиденциальности", "conditions_of_use": "Условия использования", "terms_of_use": "Условия использования", "about_us": "О нас", "change_password": "Изменить пароль", "current_password": "Текущий пароль", "new_password": "Новый пароль", "password_confirmation": "Подтверждение пароля", "cancel": "Отмена", "confirm": "Подтвердить", "delete": "Удалить", "password_updated": "Вы обновили свой пароль!", "language": "Язык", "currency": "Валюта", "currency_updated": "Валюта была обновлена", "errors": {"not_authenticated": "Вы не вошли в систему, пожалуйста, войдите.", "update_error": "Ошибка при обновлении профиля, попробуйте снова.", "logout_error": "Ошибка при выходе из системы, попробуйте снова.", "account_delete_error": "Ошибка при удалении аккаунта, попробуйте снова.", "password_update_error": "Ошибка при обновлении пароля, попробуйте снова.", "old_password_required": "Необходимо указать текущий пароль", "new_password_required": "Необходимо указать новый пароль", "password_confirmation_required": "Необходимо подтвердить пароль", "password_mismatch": "Новый пароль и подтверждение не совпадают"}}}