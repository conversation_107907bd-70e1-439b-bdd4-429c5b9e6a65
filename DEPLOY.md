# Deployment Guide - Mon parfum idéal

This guide covers how to deploy the Mon parfum idéal React Native Expo app using EAS (Expo Application Services).

## Prerequisites

### 1. EAS CLI Installation
```bash
npm install -g @expo/eas-cli
```

### 2. Expo Account Login
```bash
eas login
```

### 3. Project Configuration
Ensure you're in the project directory with valid `app.json` and `eas.json` configurations.

## Project Configuration

### EAS Project Details
- **Project ID**: `ad4617d9-2e18-424d-9eb4-5ba55f4a4bd2`
- **Owner**: `ledhcg`
- **Slug**: `mon-parfum-ideal`
- **Bundle ID (iOS)**: `fr.lunify.mpi`
- **Package Name (Android)**: `fr.lunify.mpi`

### Build Profiles

The project includes several build profiles defined in `eas.json`:

#### 1. Development Build
```bash
eas build --profile development --platform all
```
- Creates development client for testing
- Internal distribution only
- Includes development tools

#### 2. Preview Build
```bash
eas build --profile preview --platform all
```
- Internal distribution
- Production-like environment
- Auto-increments version
- Environment: `preview`
- API URL: `https://api.monparfumidealmpi.com`

#### 3. Sandbox Build
```bash
eas build --profile sandbox --platform all
```
- Testing environment
- Auto-increments version
- Environment: `sandbox`
- API URL: `https://api.monparfumidealmpi.com`

#### 4. Production Build
```bash
eas build --profile production --platform all
```
- Production-ready build
- Auto-increments version
- Environment: `production`
- API URL: `https://api.monparfumidealmpi.com`
- Android: Generates App Bundle (AAB)
- Uses local credentials

#### 5. Preview APK Build
```bash
eas build --profile preview-apk --platform android
```
- Generates APK file for easy distribution
- Production environment settings
- Android only

## Build Commands

### Building for Specific Platforms

#### iOS Only
```bash
eas build --profile production --platform ios
```

#### Android Only
```bash
eas build --profile production --platform android
```

#### Both Platforms
```bash
eas build --profile production --platform all
```

### Build Status and Management
```bash
# Check build status
eas build:list

# View specific build details
eas build:view [BUILD_ID]

# Cancel running build
eas build:cancel [BUILD_ID]
```

## App Store Submission

### Configure Submission Profiles
The project has submission profiles for both sandbox and production environments in `eas.json`.

### Submit to App Stores
```bash
# Submit to both stores (production)
eas submit --profile production --platform all

# Submit to specific store
eas submit --profile production --platform ios
eas submit --profile production --platform android

# Submit sandbox build
eas submit --profile sandbox --platform all
```

### Manual Submission
If you prefer manual submission:
1. Download the build artifacts from EAS dashboard
2. Upload iOS builds (.ipa) to App Store Connect
3. Upload Android builds (.aab) to Google Play Console

## Credentials Management

### iOS Credentials
```bash
# Configure iOS credentials
eas credentials -p ios

# View current credentials
eas credentials:list
```

### Android Credentials
```bash
# Configure Android credentials
eas credentials -p android

# Generate new keystore (if needed)
eas credentials -p android --clear-credentials
```

## Over-the-Air Updates (OTA)

The app is configured for OTA updates with version-based runtime policy.

### Publish Updates
```bash
# Publish to default branch
eas update

# Publish to specific branch
eas update --branch preview

# Publish with message
eas update --message "Bug fixes and performance improvements"
```

### Update Channels
```bash
# View update channels
eas channel:list

# Create new channel
eas channel:create [CHANNEL_NAME]

# Point channel to specific branch
eas channel:edit [CHANNEL_NAME] --branch [BRANCH_NAME]
```

## Environment Variables

### Build-time Environment Variables
Set in `eas.json` under each build profile:
- `EXPO_PUBLIC_ENV`: Environment identifier
- `EXPO_PUBLIC_API_URL`: Backend API URL
- `NODE_OPTIONS`: Node.js memory settings
- `SENTRY_ALLOW_FAILURE`: Sentry error handling

### Secrets Management
```bash
# Add secret environment variable
eas secret:create --scope project --name SECRET_NAME --value SECRET_VALUE

# List secrets
eas secret:list

# Delete secret
eas secret:delete --name SECRET_NAME
```

## Monitoring and Analytics

### Sentry Integration
- **Organization**: `lunify`
- **Project**: `mpi-mobile`
- **URL**: `https://sentry.lunify.fr`

Sentry is automatically configured and will track crashes and performance in production builds.

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear build cache
eas build --profile production --clear-cache

# Check build logs
eas build:view [BUILD_ID]
```

#### Credential Issues
```bash
# Reset iOS credentials
eas credentials -p ios --clear-credentials

# Reset Android credentials
eas credentials -p android --clear-credentials
```

#### Update Issues
```bash
# Check update status
eas update:list

# Rollback to previous update
eas update:rollback
```

### Getting Help
- EAS Documentation: https://docs.expo.dev/eas/
- Expo Discord: https://discord.gg/expo
- GitHub Issues: Check project repository for known issues

## Deployment Checklist

### Pre-deployment
- [ ] Update version in `app.json`
- [ ] Test app thoroughly on both platforms
- [ ] Verify environment variables are correct
- [ ] Ensure all secrets are properly configured
- [ ] Check that required certificates are valid

### Deployment
- [ ] Run appropriate build command
- [ ] Monitor build progress in EAS dashboard
- [ ] Test downloaded build on physical devices
- [ ] Submit to app stores (if needed)
- [ ] Publish OTA update (if applicable)

### Post-deployment
- [ ] Monitor crash reports in Sentry
- [ ] Verify app functionality in production
- [ ] Update internal documentation
- [ ] Notify team of successful deployment

## Useful Links

- **EAS Dashboard**: https://expo.dev/accounts/ledhcg/projects/mon-parfum-ideal
- **Update URL**: https://u.expo.dev/ad4617d9-2e18-424d-9eb4-5ba55f4a4bd2
- **Sentry Dashboard**: https://sentry.lunify.fr/organizations/lunify/projects/mpi-mobile/