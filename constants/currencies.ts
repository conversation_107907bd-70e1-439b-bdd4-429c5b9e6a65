export interface Currency {
  code: string;
  name: string;
  symbol: string;
  rate: number; // Exchange rate relative to EUR (EUR = 1)
}

export const SUPPORTED_CURRENCIES: Currency[] = [
  { code: "EUR", name: "Euro", symbol: "€", rate: 1 },
  { code: "USD", name: "US Dollar", symbol: "$", rate: 1.08 },
  { code: "GBP", name: "British Pound", symbol: "£", rate: 0.85 },
  { code: "JPY", name: "Japanese Yen", symbol: "¥", rate: 162.5 },
  { code: "CNY", name: "Chinese Yuan", symbol: "¥", rate: 7.8 },
  { code: "CAD", name: "Canadian Dollar", symbol: "CA$", rate: 1.47 },
  { code: "AUD", name: "Australian Dollar", symbol: "A$", rate: 1.64 },
  { code: "CHF", name: "Swiss Franc", symbol: "Fr", rate: 0.97 },
  { code: "KRW", name: "South Korean Won", symbol: "₩", rate: 1485 },
  { code: "INR", name: "Indian Rupee", symbol: "₹", rate: 90.3 },
  { code: "RUB", name: "Russian Ruble", symbol: "₽", rate: 100.2 },
  { code: "SGD", name: "Singapore Dollar", symbol: "S$", rate: 1.45 },
  { code: "NZD", name: "New Zealand Dollar", symbol: "NZ$", rate: 1.76 },
  { code: "HKD", name: "Hong Kong Dollar", symbol: "HK$", rate: 8.4 },
  { code: "SEK", name: "Swedish Krona", symbol: "kr", rate: 11.3 },
  { code: "NOK", name: "Norwegian Krone", symbol: "kr", rate: 11.4 },
  { code: "DKK", name: "Danish Krone", symbol: "kr", rate: 7.45 },
  { code: "PLN", name: "Polish Złoty", symbol: "zł", rate: 4.3 },
  { code: "THB", name: "Thai Baht", symbol: "฿", rate: 39.1 },
  { code: "MXN", name: "Mexican Peso", symbol: "$", rate: 18.1 },
  { code: "AED", name: "UAE Dirham", symbol: "د.إ", rate: 4.17 },
];

// Default currency
export const DEFAULT_CURRENCY = "EUR";
