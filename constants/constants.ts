import HesperidesIcon from "@/assets/images/perfume-families/icon-hesperides.svg";
import FloralIcon from "@/assets/images/perfume-families/icon-floral.svg";
import WoodyIcon from "@/assets/images/perfume-families/icon-woody.svg";
import FernIcon from "@/assets/images/perfume-families/icon-fern.svg";
import ChypreIcon from "@/assets/images/perfume-families/icon-chypre.svg";
import OrientalIcon from "@/assets/images/perfume-families/icon-oriental.svg";
import AromaticIcon from "@/assets/images/perfume-families/icon-aromatic.svg";
import { components } from "@/@types/api.types";
import i18n, { i18nEvents, LANGUAGE_CHANGE_EVENT } from "@/utils/i18n";

export const ROLES = {
  ADMIN: "admin",
  USER: "user",
};

export const GENDER = {
  FEMME: "F",
  HOMME: "M",
  ALL: "all",
};

export const PERFUME_SEASONS = {
  cold: "Saisons froides",
  warm: "Saisons chaudes",
  cold_warm: "Toutes saisons",
};

// Định nghĩa FAMILIES trước khi được sử dụng trong createTranslatedConstants

export const BOTTOM_SHEET_KEYS = {
  OLFACTORY: "olfactory",
  TOP: "top",
  BASE: "base",
  HEART: "heart",
  NOTE_OLFACTIVE_1: "note_olfactive_1",
  NOTE_OLFACTIVE_2: "note_olfactive_2",
  NOTE_OLFACTIVE_3: "note_olfactive_3",
};

export const SLIDER_PRICES = ["0", "50€", "100€", "150€", "+"];

export const SLIDER_VOLUMES = ["0", "50ml", "100ml", "150ml", "+"];

// Initialize FAMILIES first with default values
export const FAMILIES = [
  {
    name: "hesperides",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-hesperides-color.jpg"),
    icon: HesperidesIcon,
  },
  {
    name: "floral",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-floral-color.jpg"),
    icon: FloralIcon,
  },
  {
    name: "woody",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-woody-color.jpg"),
    icon: WoodyIcon,
  },
  {
    name: "fern",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-fern-color.jpg"),
    icon: FernIcon,
  },
  {
    name: "chypre",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-chypre-color.jpg"),
    icon: ChypreIcon,
  },
  {
    name: "oriental",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-oriental-color.jpg"),
    icon: OrientalIcon,
  },
  {
    name: "aromatic",
    displayName: "",
    backgroundSource: require("@/assets/images/perfume-families/bg-aromatic-color.jpg"),
    icon: AromaticIcon,
  },
];

// Create a function to generate dynamic constants based on current language
export function createTranslatedConstants() {
  // Update FAMILIES display names
  const familyLabels = {
    hesperides: i18n.t("common.families.hesperides"),
    floral: i18n.t("common.families.floral"),
    woody: i18n.t("common.families.woody"),
    fern: i18n.t("common.families.fern"),
    chypre: i18n.t("common.families.chypre"),
    oriental: i18n.t("common.families.oriental"),
    aromatic: i18n.t("common.families.aromatic"),
  } as Record<string, string>;

  // Update FAMILIES displayName
  FAMILIES.forEach((family) => {
    family.displayName = familyLabels[family.name] || family.name;
  });

  return {
    GENDER_LABELS: {
      M: i18n.t("common.genders.male"),
      F: i18n.t("common.genders.female"),
      all: i18n.t("common.genders.mixed"),
    } as Record<string, string>,
    FAMILY_LABELS: familyLabels,
    TABS: {
      PERFUME: i18n.t("search.search_by_perfume"),
      BRAND: i18n.t("search.search_by_brand"),
      SUR_MESURE: i18n.t("search.search_by_criterias"),
    },
    GENDERS_DATA_SHORT: [
      { label: i18n.t("common.genders.female"), value: GENDER.FEMME },
      { label: i18n.t("common.genders.male"), value: GENDER.HOMME },
    ],
    GENDERS_DATA_FULL: [
      { label: i18n.t("common.genders.female"), value: GENDER.FEMME },
      { label: i18n.t("common.genders.male"), value: GENDER.HOMME },
      { label: i18n.t("common.genders.mixed"), value: GENDER.ALL },
    ],
    LEGAL_DATA: [
      { label: i18n.t("profile.privacy_policy"), url: "https://mpi.lunify.fr" },
      {
        label: i18n.t("profile.conditions_of_use"),
        url: "https://mpi.lunify.fr",
      },
      { label: i18n.t("profile.terms_of_use"), url: "https://mpi.lunify.fr" },
      { label: i18n.t("profile.about_us"), url: "https://mpi.lunify.fr" },
    ],
    PERFUME_TYPES: {
      parfum: i18n.t("search.perfume"),
      EXDP: i18n.t("search.perfume_extract"),
      EDP: "Eau de parfum",
      EDT: "Eau de toilette",
      EDC: "Eau de cologne",
      EF: "Eau fraîche",
    },
    BOTTOM_SHEET_DATA: new Map([
      [
        BOTTOM_SHEET_KEYS.OLFACTORY,
        {
          id: "family",
          title: i18n.t("search.by_criterias.olfactory_family"),
          description: i18n.t(
            "search.by_criterias.olfactory_family_description",
          ),
          placeholder: i18n.t("search.by_criterias.search_olfactory_family"),
          data: FAMILIES,
        },
      ],
      [
        BOTTOM_SHEET_KEYS.TOP,
        {
          id: "note_top",
          title: i18n.t("search.by_criterias.top_note"),
          description: i18n.t("search.by_criterias.top_note_description"),
          placeholder: i18n.t("search.by_criterias.search_top_note"),
        },
      ],
      [
        BOTTOM_SHEET_KEYS.BASE,
        {
          id: "note_base",
          title: i18n.t("search.by_criterias.base_note"),
          description: i18n.t("search.by_criterias.base_note_description"),
          placeholder: i18n.t("search.by_criterias.search_base_note"),
        },
      ],
      [
        BOTTOM_SHEET_KEYS.HEART,
        {
          id: "note_middle",
          title: i18n.t("search.by_criterias.heart_note"),
          description: i18n.t("search.by_criterias.heart_note_description"),
          placeholder: i18n.t("search.by_criterias.search_heart_note"),
        },
      ],
      [
        BOTTOM_SHEET_KEYS.NOTE_OLFACTIVE_1,
        {
          id: "note_olfactive_1",
          title: i18n.t("search.by_criterias.note_olfactive_1"),
          placeholder: i18n.t("search.by_criterias.select_note_olfactive_1"),
        },
      ],
      [
        BOTTOM_SHEET_KEYS.NOTE_OLFACTIVE_2,
        {
          id: "note_olfactive_2",
          title: i18n.t("search.by_criterias.note_olfactive_2"),
          placeholder: i18n.t("search.by_criterias.select_note_olfactive_2"),
        },
      ],
      [
        BOTTOM_SHEET_KEYS.NOTE_OLFACTIVE_3,
        {
          id: "note_olfactive_3",
          title: i18n.t("search.by_criterias.note_olfactive_3"),
          placeholder: i18n.t("search.by_criterias.select_note_olfactive_3"),
        },
      ],
    ]),
  };
}

// Initialize FAMILIES displayName on startup
FAMILIES.forEach((family) => {
  family.displayName = i18n.t(`common.families.${family.name}`) || family.name;
});

// Initialize constants with current language
let translatedConstants = createTranslatedConstants();

// Re-export constants
export let GENDER_LABELS: Record<string, string> =
  translatedConstants.GENDER_LABELS;
export let FAMILY_LABELS: Record<string, string> =
  translatedConstants.FAMILY_LABELS;
export let TABS = translatedConstants.TABS;
export let GENDERS_DATA_SHORT = translatedConstants.GENDERS_DATA_SHORT;
export let GENDERS_DATA_FULL = translatedConstants.GENDERS_DATA_FULL;
export let LEGAL_DATA = translatedConstants.LEGAL_DATA;
export let PERFUME_TYPES = translatedConstants.PERFUME_TYPES;
export let BOTTOM_SHEET_DATA = translatedConstants.BOTTOM_SHEET_DATA;

// Listen for language changes and update constants
i18nEvents.on(LANGUAGE_CHANGE_EVENT, () => {
  translatedConstants = createTranslatedConstants();

  // Update exported constants
  Object.assign(GENDER_LABELS, translatedConstants.GENDER_LABELS);
  Object.assign(FAMILY_LABELS, translatedConstants.FAMILY_LABELS);
  Object.assign(TABS, translatedConstants.TABS);
  Object.assign(GENDERS_DATA_SHORT, translatedConstants.GENDERS_DATA_SHORT);
  Object.assign(GENDERS_DATA_FULL, translatedConstants.GENDERS_DATA_FULL);
  Object.assign(LEGAL_DATA, translatedConstants.LEGAL_DATA);
  Object.assign(PERFUME_TYPES, translatedConstants.PERFUME_TYPES);

  // Update FAMILIES displayName
  FAMILIES.forEach((family) => {
    if (FAMILY_LABELS[family.name]) {
      family.displayName = FAMILY_LABELS[family.name];
    }
  });
});
// Dynamic PERFUME_TYPES_DATA that updates with language changes
export function getPerfumeTypesData() {
  return [
    { label: PERFUME_TYPES.parfum, value: "parfum" },
    { label: PERFUME_TYPES.EXDP, value: "EXDP" },
    { label: PERFUME_TYPES.EDP, value: "EDP" },
    { label: PERFUME_TYPES.EDT, value: "EDT" },
    { label: PERFUME_TYPES.EDC, value: "EDC" },
    { label: PERFUME_TYPES.EF, value: "EF" },
  ];
}

// Export for backward compatibility
export const PERFUME_TYPES_DATA = getPerfumeTypesData();

export function getFamilyLabel(key: string): string {
  return FAMILY_LABELS[key] || "Inconnu";
}

export function getGenderLabel(key: string): string {
  return GENDER_LABELS[key] || "Inconnu";
}

export const NOTE_TYPES = {
  BASE: "base",
  MIDDLE: "middle",
  TOP: "top",
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function groupNotesByType(notes: any[]) {
  return notes.reduce(
    (acc, note) => {
      const type = note.type;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(note);
      return acc;
    },
    {} as Record<string, components["schemas"]["Note"]>,
  );
}
