import React from "react";
import {
  DarkTheme,
  ThemeProvider as ReactNavigationThemeProvider,
} from "@react-navigation/native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";

export default function ThemeProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <GluestackUIProvider mode="dark">
        <ReactNavigationThemeProvider value={DarkTheme}>
          {children}
        </ReactNavigationThemeProvider>
      </GluestackUIProvider>
    </GestureHandlerRootView>
  );
}
