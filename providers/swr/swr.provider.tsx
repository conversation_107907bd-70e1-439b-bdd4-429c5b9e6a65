import { getFetcher } from "@/api/axios";
import { AppState } from "react-native";
import { SWRConfig } from "swr";
import React from "react";

export default function SwrProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SWRConfig
      value={{
        fetcher: getFetcher,
        provider: () => new Map(),
        isVisible: () => true,
        initFocus(callback) {
          let appState = AppState.currentState;

          const subscription = AppState.addEventListener(
            "change",
            (nextAppState) => {
              if (
                appState.match(/inactive|background/) &&
                nextAppState === "active"
              ) {
                callback();
              }
              appState = nextAppState;
            },
          );

          return () => {
            subscription.remove();
          };
        },
      }}
    >
      {children}
    </SWRConfig>
  );
}
