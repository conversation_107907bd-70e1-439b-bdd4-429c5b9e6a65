import React, { createContext, useContext, useState, useEffect } from 'react';
import i18n, { changeLanguage } from '@/utils/i18n';

interface I18nContextType {
  locale: string;
  setLocale: (locale: string) => Promise<void>;
  t: (key: string, options?: object) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

export default function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState(i18n.locale);

  // Update locale state when i18n.locale changes
  useEffect(() => {
    const updateLocale = () => {
      if (locale !== i18n.locale) {
        setLocaleState(i18n.locale);
      }
    };

    // Check periodically for changes
    const intervalId = setInterval(updateLocale, 100);
    return () => clearInterval(intervalId);
  }, [locale]);

  const setLocale = async (newLocale: string) => {
    await changeLanguage(newLocale);
    setLocaleState(newLocale);
  };

  // Wrapper for i18n.t that ensures we're using the latest translations
  const t = (key: string, options?: object) => {
    return i18n.t(key, options);
  };

  const value = {
    locale,
    setLocale,
    t
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
} 