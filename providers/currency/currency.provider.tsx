import React, { createContext, useContext, useState, useEffect } from "react";
import {
  loadCurrency,
  changeCurrency,
  getCurrentCurrency,
  convertPrice as convertPriceFunc,
  formatCurrency as formatCurrencyFunc,
  currencyEvents,
  CURRENCY_CHANGE_EVENT,
} from "@/utils/currency";
import { SUPPORTED_CURRENCIES } from "@/constants/currencies";

interface CurrencyContextType {
  currency: string;
  setCurrency: (currency: string) => Promise<boolean>;
  convertPrice: (priceInEUR: number) => number;
  formatCurrency: (price: number) => string;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(
  undefined
);

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error("useCurrency must be used within a CurrencyProvider");
  }
  return context;
};

export default function CurrencyProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [currency, setCurrencyState] = useState<string>(getCurrentCurrency());

  // Load currency when component mounts
  useEffect(() => {
    const initCurrency = async () => {
      const currentCurrency = await loadCurrency();
      setCurrencyState(currentCurrency);
    };

    initCurrency();
  }, []);

  // Update state when currency changes
  useEffect(() => {
    const handleCurrencyChange = (newCurrency: string) => {
      if (currency !== newCurrency) {
        setCurrencyState(newCurrency);
      }
    };

    // Listen for currency change events
    currencyEvents.on(CURRENCY_CHANGE_EVENT, handleCurrencyChange);

    // Clean up event listener
    return () => {
      currencyEvents.off(CURRENCY_CHANGE_EVENT, handleCurrencyChange);
    };
  }, [currency]);

  // Function to change currency
  const setCurrency = async (newCurrency: string) => {
    const success = await changeCurrency(newCurrency);
    if (success) {
      setCurrencyState(newCurrency);
    }
    return success;
  };

  // Context value
  const value = {
    currency,
    setCurrency,
    convertPrice: convertPriceFunc,
    formatCurrency: formatCurrencyFunc,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
}
