import SwrProvider from "@/providers/swr/swr.provider";
import ThemeProvider from "@/providers/theme/theme.provider";
import I18nProvider from "@/providers/i18n/i18n.provider";
import CurrencyProvider from "@/providers/currency/currency.provider";
import { store } from "@/store";
import React, { useEffect } from "react";
import { Provider as ReduxProvider, useDispatch } from "react-redux";
import { TranslatedConstantsProvider } from "@/contexts/TranslatedConstantsContext";
import { AlertProvider } from "@/components/ui/custom-alert";

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SwrProvider>
      <ThemeProvider>
        <ReduxProvider store={store}>
          <I18nProvider>
            <CurrencyProvider>
              <TranslatedConstantsProvider>
                <AlertProvider>
                  <LoadUserState>{children}</LoadUserState>
                </AlertProvider>
              </TranslatedConstantsProvider>
            </CurrencyProvider>
          </I18nProvider>
        </ReduxProvider>
      </ThemeProvider>
    </SwrProvider>
  );
}

export const LoadUserState = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch.favorites.loadFavorites();
    dispatch.favorites.loadNotes();
    dispatch.auth.refreshUser();
  }, [dispatch]);
  
  return children;
};
