import { Stack } from "expo-router";
import { View } from "react-native";
import { useSelector } from "react-redux";

export default function AppLayout() {
  // @ts-ignore
  const auth = useSelector((state) => state.auth);

  if (auth.isLoading) {
    return <View className="flex-1 bg-background-dark" />;
  }

  // if (!auth.isAuthenticated) {
  //   return <Redirect href="/onboarding" />;
  // }

  return (
    <Stack>
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  );
}
