import { SafeAreaView } from "@/components/ui/safe-area-view";
import { Text } from "@/components/ui/text";
import Divider from "@/components/layout/Divider";
import { useDispatch, useSelector } from "react-redux";
import AppButton from "@/components/common/Button";
import { router } from "expo-router";
import * as Application from "expo-application";
import { ScrollView } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import ConfirmDialog from "@/components/common/ConfirmDialog";
import React, { useState } from "react";
import { toast } from "sonner-native";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import HeaderAvatar from "@/components/profile/HeaderAvatar";
import LegalList from "@/components/profile/LegalList";
import ControlledInput from "@/components/form/ControlledInput";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ControlledRadioButtons from "@/components/form/ControlledRadioButtons";
import ControlledDateInput from "@/components/form/ControlledDateInput";
import axiosInstance from "@/api/axios";
import PasswordFormDialog from "@/components/profile/PasswordFormDialog";
import { KeyboardAvoidingView, Platform } from "react-native";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { format } from "date-fns";
import Unauthenticated from "@/components/profile/Unauthenticated";
import LanguageSelector from "@/components/profile/LanguageSelector";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import CurrencySelector from "@/components/profile/CurrencySelector";

export default function ProfileScreen() {
  const dispatch = useDispatch();
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { GENDERS_DATA_SHORT } = constants;
  // @ts-ignore
  const user = useSelector((state) => state.auth.user);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [openPasswordForm, setOpenPasswordForm] = useState(false);

  const profileSchema = z.object({
    gender: z.enum(["F", "M"], {
      message: t("auth.errors.gender_required"),
    }),
    username: z.string().min(1, t("auth.errors.username_required")).trim(),
    birthdate: z.string().min(10, t("auth.errors.birthdate_required")),
    phone: z
      .string()
      .min(10, t("auth.errors.phone_required"))
      .max(10, t("auth.errors.phone_invalid_format"))
      .trim(),
    country: z
      .string()
      .trim()
      .min(1, t("auth.errors.country_required"))
      .max(2, t("auth.errors.country_invalid_format")),
    city: z.string().min(1, t("auth.errors.city_required")).trim(),
    email: z
      .string()
      .min(1, t("auth.errors.email_required"))
      .email(t("auth.errors.email_invalid")),
  });

  type ProfileSchemaType = z.infer<typeof profileSchema>;

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { trigger, control } = useForm<ProfileSchemaType>({
    defaultValues: {
      gender: "F",
      username: "",
      birthdate: "",
      phone: "",
      country: "FR",
      city: "",
      email: "",
    },
    values: user
      ? {
          ...user,
          birthdate: format(new Date(user.birthdate), "dd/MM/yyyy"),
        }
      : null,
    mode: "onSubmit",
    resolver: zodResolver(profileSchema),
  });

  const handleValidate = async (name: string, value: string) => {
    try {
      const isValid = await trigger(name as keyof ProfileSchemaType);
      if (isValid && value !== user[name]) {
        const { data } = await axiosInstance.put("/profile", {
          [name]: value,
        });
        dispatch.auth.updateUser(data);
        toast.success(t("profile.update_success"));
      }
    } catch (e) {
      console.error(e);
      toast.error(t("profile.errors.update_error"));
    }
  };

  const handleOpenConfirm = () => {
    setOpenConfirm((oldState) => !oldState);
  };

  const handleOpenPasswordForm = () => {
    setOpenPasswordForm((oldState) => !oldState);
  };

  const handleDisconnection = async () => {
    try {
      await dispatch.auth.logout();
      if (router.canDismiss()) {
        router.dismissAll();
      }
      toast.info(t("profile.logout_success"));
      router.replace("/onboarding");
    } catch (e) {
      console.error(e);
      toast.error(t("profile.errors.logout_error"));
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await dispatch.auth.delete();
      if (router.canDismiss()) {
        router.dismissAll();
      }
      toast.info(t("profile.account_delete_success"));
      router.replace("/onboarding");
    } catch (e) {
      console.error(e);
      toast.error(t("profile.errors.account_delete_error"));
    }
  };

  if (!user) {
    return <Unauthenticated />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      style={{ flex: 1 }}
    >
      <SafeAreaView className="flex-1">
        <HeaderLogo />
        <ScrollView showsVerticalScrollIndicator={false} className="h-full">
          <HeaderAvatar user={user} />
          <VStack space="sm" className="p-4">
            <Divider />
            <VStack>
              <ControlledInput
                name="username"
                control={control}
                title={t("auth.username")}
                placeholder={t("auth.username_placeholder")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledInput
                name="email"
                control={control}
                title={t("auth.email_address")}
                placeholder={t("auth.email_placeholder")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledRadioButtons
                data={GENDERS_DATA_SHORT}
                name="gender"
                control={control}
                title={t("auth.gender")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledDateInput
                name="birthdate"
                control={control}
                title={t("auth.birthdate")}
                placeholder={t("auth.birthdate_placeholder")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledInput
                name="country"
                control={control}
                title={t("auth.country")}
                placeholder={t("auth.country_placeholder")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledInput
                name="city"
                control={control}
                title={t("auth.city")}
                placeholder={t("auth.city_placeholder")}
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />

              <ControlledInput
                name="phone"
                control={control}
                title={t("auth.phone")}
                placeholder="0600000000"
                containerClassName="py-5"
                manualMode
                onValidate={handleValidate}
              />
            </VStack>

            <VStack>
              <ControlledInput
                name="password"
                control={control}
                title={t("auth.password")}
                placeholder="***************"
                containerClassName="py-5"
                manualMode
                disabled
                onPress={handleOpenPasswordForm}
              />
            </VStack>

            <LegalList />

            <Divider />

            <LanguageSelector />

            <Divider />

            <CurrencySelector />

            <Divider />

            <AppButton className="mt-10" onPress={handleDisconnection}>
              {t("profile.logout")}
            </AppButton>
            <AppButton
              className="border-0 my-5"
              buttonTextClassName="underline text-sm"
              onPress={handleOpenConfirm}
            >
              {t("profile.delete_account")}
            </AppButton>

            <Text className="ml-4 mt-2 pb-24">
              {t("profile.version")} {Application.nativeApplicationVersion}
              {/* {Application.nativeBuildVersion} */}
            </Text>
          </VStack>
        </ScrollView>
        <ConfirmDialog
          open={openConfirm}
          onClose={handleOpenConfirm}
          title={t("profile.confirm_delete_title")}
          description={t("profile.confirm_delete_description")}
          onConfirm={handleDeleteAccount}
        />
        <PasswordFormDialog
          open={openPasswordForm}
          onClose={handleOpenPasswordForm}
        />
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}
