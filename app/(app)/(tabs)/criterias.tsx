import React, { useRef, useState } from "react";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { ScrollView } from "react-native";
import { HeaderTitle } from "@/components/layout/HeaderTitle";
import { Header<PERSON>ogo } from "@/components/layout/HeaderLogo";
import SearchByCriteriasWrapper, {
  SearchByCriteriasRef,
} from "@/components/search/SearchByCriteriasWrapper";
import { useLocalSearchParams } from "expo-router";
import AppBottomSheet, {
  BottomSheetRef,
} from "@/components/search/CustomBottomSheet";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { useI18n } from "@/providers/i18n/i18n.provider";

const DEFAULT_BOTTOM_SHEET_DATA = {
  id: null,
  title: "",
  description: "",
  data: [],
};

export default function SearchCriteriasScreen() {
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { BOTTOM_SHEET_DATA } = constants;
  const params = useLocalSearchParams<{ family?: string }>();
  
  const searchCriteriasRef = useRef<SearchByCriteriasRef | null>(null);
  const bottomSheetRef = useRef<BottomSheetRef>(null);
  const [bottomSheetInfo, setBottomSheetInfo] = useState<any>({
    ...DEFAULT_BOTTOM_SHEET_DATA,
  });

  const handleOpenBottomSheet = (id: string) => {
    setBottomSheetInfo(BOTTOM_SHEET_DATA.get(id));
    bottomSheetRef.current?.openModal();
  };

  const handleCloseBottomSheet = () => {
    bottomSheetRef.current?.closeModal();
  };

  const handleFilters = (id: string, data: any) => {
    if (searchCriteriasRef.current) {
      searchCriteriasRef.current.changeFilters(id, data);
      handleCloseBottomSheet();
    }
  };

  return (
    <SafeAreaView className="flex-1">
      <HeaderLogo />
      <ScrollView showsVerticalScrollIndicator={false}>
        <HeaderTitle title={t("layout.search")} />
        
        <SearchByCriteriasWrapper
          ref={searchCriteriasRef}
          onOpenBottomSheet={handleOpenBottomSheet}
          onCloseBottomSheet={handleCloseBottomSheet}
          family={params.family}
        />
      </ScrollView>
      
      <AppBottomSheet
        ref={bottomSheetRef}
        id={bottomSheetInfo.id}
        title={bottomSheetInfo.title}
        description={bottomSheetInfo.description}
        placeholder={bottomSheetInfo.placeholder}
        existingData={bottomSheetInfo.data}
        onPress={handleFilters}
      />
    </SafeAreaView>
  );
}