import { Tabs } from "expo-router";
import React, { useEffect, useState } from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";

import HomeIcon from "@/assets/images/icons/navbar/home.svg";
import SearchIcon from "@/assets/images/icons/navbar/magnify.svg";
import FavoritesIcon from "@/assets/images/icons/navbar/star.svg";
import ProfileIcon from "@/assets/images/icons/navbar/user.svg";
import { useI18n } from "@/providers/i18n/i18n.provider";

export default function TabLayout() {
  const { t, locale } = useI18n();
  const [tabTitles, setTabTitles] = useState({
    home: t("layout.home"),
    search: t("layout.search"),
    favorites: t("layout.favorites"),
    profile: t("layout.profile"),
  });

  // Update tab titles when language changes
  useEffect(() => {
    setTabTitles({
      home: t("layout.home"),
      search: t("layout.search"),
      favorites: t("layout.favorites"),
      profile: t("layout.profile"),
    });
  }, [locale, t]);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: "#BF9E57",
        tabBarInactiveTintColor: "#F2ECE0",
        tabBarLabelStyle: {
          fontFamily: "Overpass_700Bold",
          marginTop: 5,
          fontSize: 12,
        },
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: "absolute",
            height: 85,
            paddingBottom: 20,
          },
          android: {
            paddingVertical: 5,
            height: 65,
            paddingBottom: 10,
          },
          default: {
            height: 75,
            paddingBottom: 15,
          },
        }),
      }}
    >
      <Tabs.Screen
        name="(home)"
        options={{
          title: tabTitles.home,
          tabBarIcon: ({ color }) => (
            <HomeIcon width={24} height={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="(search)"
        options={{
          title: tabTitles.search,
          tabBarIcon: ({ color }) => (
            <SearchIcon width={24} height={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="(favorites)"
        options={{
          title: tabTitles.favorites,
          tabBarIcon: ({ color }) => (
            <FavoritesIcon width={24} height={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: tabTitles.profile,
          tabBarIcon: ({ color }) => (
            <ProfileIcon width={24} height={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="index"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="criterias"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="criterias-results"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}
