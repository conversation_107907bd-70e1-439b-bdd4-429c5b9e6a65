import React, { useEffect, useRef, useState } from "react";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { TouchableOpacity, Text, ScrollView } from "react-native";
import { HeaderTitle } from "@/components/layout/HeaderTitle";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import SearchByPerfume from "@/components/search/SearchByPerfume";
import SearchByBrand from "@/components/search/SearchByBrand";
import { HStack } from "@/components/ui/hstack";
import { useLocalSearchParams } from "expo-router";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { useI18n } from "@/providers/i18n/i18n.provider";
import { FeaturedPerfumesSection } from "@/components/perfumes/FeaturedPerfumesSection";
import { FeaturedBrandsSection } from "@/components/perfumes/FeaturedBrandsSection";

export default function SearchScreen() {
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { TABS } = constants;
  const params = useLocalSearchParams<{ tab?: string }>();
  
  const [activeTab, setActiveTab] = useState(params.tab || TABS.PERFUME);
  const searchByPerfumeRef = useRef<any>(null);
  const searchByBrandRef = useRef<any>(null);



  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: any) => {
    const paddingToBottom = 50;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  useEffect(() => {
    if (params.tab) {
      setActiveTab(params.tab);
    }
  }, [params.tab]);

  return (
    <SafeAreaView className="flex-1">
      <HeaderLogo />
      <ScrollView
        stickyHeaderIndices={[1]}
        showsVerticalScrollIndicator={false}
        onScroll={({ nativeEvent }) => {
          if (isCloseToBottom(nativeEvent)) {
            if (activeTab === TABS.PERFUME) {
              searchByPerfumeRef?.current.handleReachEnd();
            } else if (activeTab === TABS.BRAND) {
              searchByBrandRef?.current.handleReachEnd();
            }
          }
        }}
        scrollEventThrottle={1000}
      >
        <HeaderTitle title={t("layout.search")} />
        
        <HStack className="border-b-[0.5px] border-grey-500 bg-grey-card">
          <TouchableOpacity
            className={`flex-1 items-center justify-center py-3 ${
              activeTab === TABS.PERFUME
                ? "border-b-[1px] border-[#BF9E57] bg-grey-card"
                : "bg-black"
            }`}
            onPress={() => setActiveTab(TABS.PERFUME)}
          >
            <Text
              className={`text-base ${
                activeTab === TABS.PERFUME
                  ? "text-gold-ultralight font-bold"
                  : "text-gray-400"
              }`}
              allowFontScaling={false}
            >
              {TABS.PERFUME}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`flex-1 items-center justify-center py-3 ${
              activeTab === TABS.BRAND
                ? "border-b-[1px] border-[#BF9E57] bg-grey-card"
                : "bg-black"
            }`}
            onPress={() => setActiveTab(TABS.BRAND)}
          >
            <Text
              className={`text-base ${
                activeTab === TABS.BRAND
                  ? "text-gold-ultralight font-bold"
                  : "text-gray-400"
              }`}
              allowFontScaling={false}
            >
              {TABS.BRAND}
            </Text>
          </TouchableOpacity>
        </HStack>

        

        {activeTab === TABS.PERFUME && (
          <>
            <FeaturedPerfumesSection />
            <SearchByPerfume ref={searchByPerfumeRef} />
          </>
        )}
        {activeTab === TABS.BRAND && (
          <>
            <FeaturedBrandsSection 
              onBrandSelect={(brand) => {
                searchByBrandRef?.current?.handleBrandSearch?.(brand);
              }}
            />
            <SearchByBrand ref={searchByBrandRef} />
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
