import React, { useState, useMemo } from "react";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { TouchableOpacity } from "react-native";
import { Text } from "@/components/ui/text";
import { ScrollView } from "@/components/ui/scroll-view";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import { HeaderTitle } from "@/components/layout/HeaderTitle";
import AppButton from "@/components/common/Button";
import Divider from "@/components/layout/Divider";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import { useSelector } from "react-redux";
import { buildFilterQuery } from "@/utils/helpers";
import { ResultsListSearch } from "@/components/search/ResultsListSearch";
import { ResultsListSimilar } from "@/components/search/ResultsListSimilar";
import { ResultsHeaderSearch } from "@/components/search/ResultsHeaderSearch";
import { ResultsHeaderSimilar } from "@/components/search/ResultsHeaderSimilar";

import { useI18n } from "@/providers/i18n/i18n.provider";
import { useCurrency } from "@/providers/currency/currency.provider";

interface LocalSearchParams {
  perfume_id: string;
  tab: string;
}

export default function ResultsScreen() {
  const { t } = useI18n();
  const { convertPrice, formatCurrency, currency } = useCurrency();
  const constants = useTranslatedConstants();
  const { TABS } = constants;
  // @ts-ignore
  const filters = useSelector((state) => state.search);
  const router = useRouter();
  const params = useLocalSearchParams<LocalSearchParams | any>();

  const prices = [
    { label: "0 - 50€", min: 0, max: 50 },
    { label: "50 - 100€", min: 50, max: 100 },
    { label: "100 - 150€", min: 100, max: 150 },
    { label: "+ 150€", min: 150, max: null },
  ];

  // Tạo label hiển thị tiền tệ hiện tại cho prices
  const pricesWithCurrentCurrency = useMemo(() => {
    return prices.map((price) => ({
      ...price,
      displayLabel:
        price.max === null
          ? `+ ${formatCurrency(convertPrice(price.min))}`
          : `${formatCurrency(convertPrice(price.min))} - ${formatCurrency(convertPrice(price.max))}`,
    }));
  }, [currency, prices, convertPrice, formatCurrency]);

  const [selectedPrice, setSelectedPrice] = useState<{
    label: string;
    min: number;
    max: number | null;
  } | null>(null);

  return (
    <SafeAreaView>
      <HeaderLogo />
      <ScrollView showsVerticalScrollIndicator={false}>
        <VStack className="pb-24">
          <HeaderTitle title={t("search.results.searchResults")} />

          <VStack className="mx-4">
            {params.tab === TABS.PERFUME ? (
              <ResultsHeaderSimilar perfumeId={params.perfume_id} />
            ) : (
              <ResultsHeaderSearch filters={filters} />
            )}

            <AppButton
              className="flex-1 border-gold-ultralight mt-4 mb-4"
              onPress={() => router.back()}
            >
              {t("search.results.modifySelection")}
            </AppButton>
            {params.tab === TABS.PERFUME && prices.length > 1 && (
              <HStack
                space="xl"
                className="pt-8 justify-center items-center flex-wrap gap-x-4 gap-y-2"
              >
                {pricesWithCurrentCurrency.map((price, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() =>
                      setSelectedPrice(
                        selectedPrice?.label === price.label ? null : price
                      )
                    }
                    activeOpacity={0.6}
                  >
                    <Text
                      className={
                        selectedPrice?.label === price.label
                          ? "text-gold-ultralight font-body-bold border-b-2 border-[#BF9E57]"
                          : "text-grey-400 border-b border-grey-600 px-1"
                      }
                    >
                      {price.displayLabel}
                    </Text>
                  </TouchableOpacity>
                ))}
              </HStack>
            )}

            {params.tab === TABS.PERFUME ? (
              <ResultsListSimilar
                perfumeId={params.perfume_id}
                price={selectedPrice}
              />
            ) : (
              <ResultsListSearch filters={buildFilterQuery(filters)} />
            )}

            <Divider className="mt-4 mb-14" />
          </VStack>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
