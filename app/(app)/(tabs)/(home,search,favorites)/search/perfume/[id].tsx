import { components } from "@/@types/api.types";
import { ScrollView } from "@/components/ui/scroll-view";
import useSWR from "swr";
import { useLocalSearchParams } from "expo-router";
import { VStack } from "@/components/ui/vstack";
import { Spinner } from "@/components/ui/spinner";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import { SafeAreaView } from "react-native";
import LayoutDivider from "@/components/layout/Divider";

import { PerfumeInfoSection } from "@/components/perfumes/sections/PerfumeInfoSection";
// import { PerfumeHistorySection } from "@/components/perfumes/sections/PerfumeHistorySection";
import { PerfumeCompositionSection } from "@/components/perfumes/sections/PerfumeCompositionSection";
import { PerfumeRadarChartSection } from "@/components/perfumes/sections/PerfumeRadarChartSection";
import { PerfumeSimilarSection } from "@/components/perfumes/sections/PerfumeSimilarSection";
import PerfumeHeaderBar from "@/components/perfumes/PerfumeHeaderBar";

export default function PerfumeShowLayout() {
  const params = useLocalSearchParams<{ id: string }>();
  const { data: perfume, isLoading } = useSWR<components["schemas"]["Perfume"]>(
    `/perfumes/${params.id}`,
    { keepPreviousData: true },
  );

  if (isLoading || !perfume) {
    return (
      <SafeAreaView className="flex-1">
        <VStack className="justify-center items-center flex-1">
          <Spinner />
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1">
      <HeaderLogo />
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="p-4 gap-14 pb-[120px]">
          <PerfumeHeaderBar perfumeId={perfume.id} />

          <PerfumeInfoSection perfume={perfume} />

          {/* {perfume.description && <PerfumeHistorySection perfume={perfume} />} */}

          <PerfumeCompositionSection perfume={perfume} />

          <LayoutDivider className="-mt-8 -mb-4" />

          <PerfumeRadarChartSection perfume={perfume} />

          <PerfumeSimilarSection perfume={perfume} />
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
