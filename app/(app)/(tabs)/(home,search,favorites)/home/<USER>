import { SafeAreaView } from "@/components/ui/safe-area-view";
import { VStack } from "@/components/ui/vstack";
import { FeaturedPerfumesSection } from "@/components/home/<USER>";
import { ScrollView } from "@/components/ui/scroll-view";
import { PerfumeInfosSection } from "@/components/home/<USER>";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import { HomeSection } from "@/components/home/<USER>";
import { AboutSection } from "@/components/home/<USER>";

export default function HomeScreen() {
  return (
    <SafeAreaView>
      <HeaderLogo />
      <ScrollView showsVerticalScrollIndicator={false}>
        <VStack space="4xl" className="pb-[120px]">
          <HomeSection />
          <FeaturedPerfumesSection />
          <PerfumeInfosSection />
          <AboutSection />
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
