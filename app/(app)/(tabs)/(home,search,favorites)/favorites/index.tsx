import Divider from "@/components/layout/Divider";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { VStack } from "@/components/ui/vstack";
import { ScrollView } from "@/components/ui/scroll-view";
import { HorizontalPerfumeCard } from "@/components/perfumes/HorizontalPerfumeCard";
import { HeaderTitle } from "@/components/layout/HeaderTitle";
import { HeaderLogo } from "@/components/layout/HeaderLogo";
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { store } from "@/store";
import { connect, useDispatch, useSelector } from "react-redux";
import { TouchableOpacity, StyleSheet } from "react-native";
import { useRouter } from "expo-router";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { SkeletonCard } from "@/components/search/SkeletonResults";
import { useI18n } from "@/providers/i18n/i18n.provider";
import Unauthenticated from "@/components/profile/Unauthenticated";
import { PerfumeNoteInput } from "@/components/perfumes/PerfumeNoteInput";

function FavoritesScreen({ perfumesFav }: { perfumesFav: number[] }) {
  const { t } = useI18n();
  // @ts-ignore
  const user = useSelector((state) => state.auth.user);

  if (!user) {
    return <Unauthenticated />;
  }

  return (
    <SafeAreaView className="flex-1">
      <HeaderLogo />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}
      >
        <HeaderTitle title={t("favorites.yourFavorites")} />

        <VStack space="lg" className="mb-[120px] p-4">
          <Divider />

          {perfumesFav.length === 0 && (
            <Card className="rounded-none">
              <Text className="text-center">{t("favorites.noFavorites")}</Text>
            </Card>
          )}

          {perfumesFav.map((id) => (
            <PerfumeItem key={id} perfumeId={id} />
          ))}

          <Divider />
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}

const mapState = (state: any) => ({
  perfumesFav: state.favorites.perfumeIds,
});

export default connect(mapState)(FavoritesScreen);

function PerfumeItem({ perfumeId }: { perfumeId: string | number }) {
  const router = useRouter();
  const { data: perfume, isLoading } = useSWR<components["schemas"]["Perfume"]>(
    `/perfumes/${perfumeId}`,
    { keepPreviousData: true },
  );

  const dispatch = useDispatch();

  const openPerfume = () => {
    router.push(`/(app)/(tabs)/(favorites)/search/perfume/${perfumeId}`);
  };

  if (isLoading || !perfume) return <SkeletonCard />;

  return (
    <VStack space="xs">
      <TouchableOpacity onPress={openPerfume} activeOpacity={0.6}>
        <HorizontalPerfumeCard
          perfume={perfume}
          handleMarkAsFavorites={() => {
            dispatch.favorites.toggleFavorite(perfumeId);
          }}
        />
      </TouchableOpacity>

      <PerfumeNoteInput
        perfumeId={
          typeof perfumeId === "string" ? parseInt(perfumeId) : perfumeId
        }
      />
    </VStack>
  );
}

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
  },
});
