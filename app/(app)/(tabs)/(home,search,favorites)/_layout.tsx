import { Stack } from "expo-router";

export default function DynamicLayout({ segment }: { segment: string }) {
  if (segment === "(favorites)") {
    return (
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="favorites/index" />
        <Stack.Screen name="search/perfume/[id]" />
      </Stack>
    );
  }

  if (segment === "(search)") {
    return (
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="search/index" />
        <Stack.Screen name="search/perfume/[id]" />
        <Stack.Screen name="search/brand/[id]" />
        <Stack.Screen name="search/results" />
      </Stack>
    );
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="home/index" />
      <Stack.Screen name="search/perfume/[id]" />
    </Stack>
  );
}
