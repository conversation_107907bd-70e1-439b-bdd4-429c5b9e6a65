import { Safe<PERSON>reaView } from "react-native-safe-area-context";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/layout/HeaderLogo";
import { ScrollView } from "@/components/ui/scroll-view";
import ControlledInput from "@/components/form/ControlledInput";
import { VStack } from "@/components/ui/vstack";
import BackButton from "@/components/common/BackButton";
import Divider from "@/components/layout/Divider";
import AppButton from "@/components/common/Button";
import { router } from "expo-router";
import GradientButton from "@/components/common/GradientButton";
import { HStack } from "@/components/ui/hstack";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dispatch } from "@/store";
import { useDispatch } from "react-redux";
import { toast } from "sonner-native";
import { KeyboardAvoidingView, Platform, View } from "react-native";
import { useI18n } from "@/providers/i18n/i18n.provider";

export default function ForgotPasswordScreen() {
  const dispatch = useDispatch<Dispatch>();
  const { t } = useI18n();

  const forgotPasswordSchema = z.object({
    email: z
      .string()
      .min(1, t("auth.errors.email_required"))
      .email(t("auth.errors.email_invalid")),
  });

  type ForgotPasswordSchemaType = z.infer<typeof forgotPasswordSchema>;

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<ForgotPasswordSchemaType>({
    defaultValues: {
      email: "",
    },
    mode: "onSubmit",
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (form: ForgotPasswordSchemaType) => {
    try {
      await dispatch.auth.forgotPassword(form);
      toast.success(t("auth.reset_email_sent"));
    } catch (e) {
      console.error(e);
      toast.error(t("auth.errors.reset_email_error"));
    }
  };

  return (
    <View className="flex-1 bg-background-dark">
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={{ flex: 1 }}
      >
        <SafeAreaView className="flex-1">
          <HeaderLogo />
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="h-full p-4"
          >
            <VStack space="sm">
              <BackButton title={t("auth.forgot_password")} />
              <Divider />
              <ControlledInput
                name="email"
                control={control}
                title={t("auth.email_address")}
                placeholder={t("auth.email_placeholder")}
              />

              <AppButton
                className="border-0 mt-2 mb-5"
                buttonTextClassName="underline"
                onPress={() => router.back()}
              >
                {t("auth.back_to_login")}
              </AppButton>
              <Divider />
            </VStack>
          </ScrollView>
        </SafeAreaView>
        <HStack className="absolute bottom-16 px-10">
          <GradientButton
            className="w-full"
            onPress={handleSubmit(onSubmit)}
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            {t("auth.reset_password")}
          </GradientButton>
        </HStack>
      </KeyboardAvoidingView>
    </View>
  );
}
