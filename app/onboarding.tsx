import {
  View,
  FlatList,
  ImageSourcePropType,
  GestureResponderEvent,
  Dimensions,
} from "react-native";
import React, { useEffect, useMemo, useRef, useState } from "react";
import OnboardingItem from "@/components/onboarding/OnboardingItem";
import OnboardingPagination from "@/components/onboarding/OnboardingPagination";
import OnboardingButtons from "@/components/onboarding/OnboardingButtons";
import { useI18n } from "@/providers/i18n/i18n.provider";

export interface OnboardingElement {
  id: number;
  image: ImageSourcePropType;
}

const slides_fr: OnboardingElement[] = [
  { id: 1, image: require("@/assets/images/onboarding/fr/slide1_fr.png") },
  { id: 2, image: require("@/assets/images/onboarding/fr/slide2_fr.png") },
  { id: 3, image: require("@/assets/images/onboarding/fr/slide3_fr.png") },
  { id: 4, image: require("@/assets/images/onboarding/fr/slide4_fr.png") },
];

const slides_en: OnboardingElement[] = [
  { id: 1, image: require("@/assets/images/onboarding/en/slide1_en.png") },
  { id: 2, image: require("@/assets/images/onboarding/en/slide2_en.png") },
  { id: 3, image: require("@/assets/images/onboarding/en/slide3_en.png") },
  { id: 4, image: require("@/assets/images/onboarding/en/slide4_en.png") },
];

export default function OnBoardingScreen() {
  const [index, setIndex] = useState(0);
  const screenWidth = Dimensions.get("window").width;
  const [isAutoplaying, setIsAutoplaying] = useState(true);
  const autoplayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const flatListRef = useRef<FlatList<OnboardingElement>>(null);
  const viewableItemsChanged = useRef(({ viewableItems }: any) => {
    setIndex(viewableItems[0].index);
  }).current;
  const isScrolling = useRef(false);
  const { locale } = useI18n();

  const viewConfig = useRef({ viewAreaCoveragePercentThreshold: 50 }).current;

  const slides = useMemo(() => {
    if (locale.includes("fr")) {
      return slides_fr;
    }
    return slides_en;
  }, [locale]);

  const scrollToIndex = (index: number) => {
    if (flatListRef.current) {
      isScrolling.current = true;
      flatListRef.current.scrollToIndex({
        index: index,
        animated: true,
      });
    }
  };

  useEffect(() => {
    if (!isAutoplaying) return;

    const interval = setInterval(() => {
      setIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % slides.length;
        scrollToIndex(nextIndex);
        return nextIndex;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [slides.length, isAutoplaying]);

  const resetAutoplay = () => {
    setIsAutoplaying(false);
    if (autoplayTimeoutRef.current) clearTimeout(autoplayTimeoutRef.current);

    autoplayTimeoutRef.current = setTimeout(() => {
      setIsAutoplaying(true);
    }, 3000);
  };

  const handleScrollEnd = () => {
    isScrolling.current = false;
  };

  const handleTouchStart = (event: GestureResponderEvent) => {
    if (isScrolling.current) return;

    const touchX = event.nativeEvent.locationX;
    if (touchX > screenWidth / 2) {
      setIndex((prevIndex) => {
        if (prevIndex >= 3) return prevIndex;
        const nextIndex = prevIndex + 1;
        scrollToIndex(nextIndex);
        return nextIndex;
      });
    } else {
      setIndex((prevIndex) => {
        if (prevIndex <= 0) return prevIndex;
        const nextIndex = prevIndex - 1;
        scrollToIndex(nextIndex);
        return nextIndex;
      });
    }
    resetAutoplay();
  };

  return (
    <View className="flex-1 bg-background-dark">
      <OnboardingPagination data={slides} currentIndex={index} />
      <FlatList
        ref={flatListRef}
        data={slides}
        scrollEnabled={false}
        onTouchStart={handleTouchStart}
        onMomentumScrollEnd={handleScrollEnd}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => <OnboardingItem item={item} />}
        horizontal
        pagingEnabled
        onViewableItemsChanged={viewableItemsChanged}
        viewabilityConfig={viewConfig}
      />
      <OnboardingButtons />
    </View>
  );
}
