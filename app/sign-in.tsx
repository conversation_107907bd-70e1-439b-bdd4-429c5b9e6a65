import { Safe<PERSON>reaView } from "react-native-safe-area-context";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/layout/HeaderLogo";
import { ScrollView } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import BackButton from "@/components/common/BackButton";
import Divider from "@/components/layout/Divider";
import AppButton from "@/components/common/Button";
import { router } from "expo-router";
import GradientButton from "@/components/common/GradientButton";
import { HStack } from "@/components/ui/hstack";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, AlertIcon, AlertText } from "@/components/ui/alert";
import ControlledInput from "@/components/form/ControlledInput";
import ControlledPasswordInput from "@/components/form/ControlledPasswordInput";
import { Dispatch } from "@/store";
import { useDispatch } from "react-redux";
import { toast } from "sonner-native";
import React from "react";
import { KeyboardAvoidingView, Platform, View } from "react-native";
import { useI18n } from "@/providers/i18n/i18n.provider";

const SignInScreen = () => {
  const { t } = useI18n();
  const dispatch = useDispatch<Dispatch>();

  const loginSchema = z.object({
    email: z
      .string()
      .min(1, t("auth.errors.email_required"))
      .email(t("auth.errors.email_invalid")),
    password: z.string().min(1, t("auth.errors.password_required")),
  });

  type LoginSchemaType = z.infer<typeof loginSchema>;

  const {
    // reset,
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginSchemaType>({
    defaultValues: {
      email: "",
      password: "",
    },
    mode: "onSubmit",
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (form: LoginSchemaType) => {
    try {
      await dispatch.auth.login(form);
      if (router.canDismiss()) {
        router.dismissAll();
      }
      router.replace("/(app)/(tabs)");
    } catch (e) {
      console.error(e);
      toast.error(t("auth.errors.invalid_credentials"));
    }
  };

  return (
    <View className="flex-1 bg-background-dark">
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={{ flex: 1 }}
      >
        <SafeAreaView className="flex-1">
          <HeaderLogo />
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="p-4 h-full"
          >
            <VStack space="sm">
              <BackButton title={t("auth.login")} />
              <Divider />

              {(errors.email || errors.password) && (
                <Alert action="error" variant="solid">
                  <AlertIcon />
                  <AlertText>
                    {t("auth.errors.invalid_credentials")}
                  </AlertText>
                </Alert>
              )}

              <ControlledInput
                name="email"
                control={control}
                title={t("auth.email_address")}
                placeholder={t("auth.email_placeholder")}
              />

              <ControlledPasswordInput
                name="password"
                control={control}
                title={t("auth.password")}
                placeholder={t("auth.password")}
              />

              <AppButton
                className="border-0 mt-2 mb-5"
                buttonTextClassName="underline"
                onPress={() => router.push("/forgot-password")}
              >
                {t("auth.forgot_password")}
              </AppButton>
              <Divider />
            </VStack>
          </ScrollView>
        </SafeAreaView>
        <HStack className="absolute bottom-16 px-10">
          <GradientButton
            className="w-full"
            onPress={handleSubmit(onSubmit)}
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            {t("auth.login_button")}
          </GradientButton>
        </HStack>
      </KeyboardAvoidingView>
    </View>
  );
};

export default SignInScreen;
