import React, { useState } from "react";
import { <PERSON>er<PERSON><PERSON> } from "@/components/layout/HeaderLogo";
import { ScrollView } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import BackButton from "@/components/common/BackButton";
import Divider from "@/components/layout/Divider";
import { HStack } from "@/components/ui/hstack";
import AppButton from "@/components/common/Button";
import GradientButton from "@/components/common/GradientButton";
import { Text } from "@/components/ui/text";
import ChevronRight from "@/assets/images/icons/chevron_right.svg";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ControlledInput from "@/components/form/ControlledInput";
import ControlledPasswordInput from "@/components/form/ControlledPasswordInput";
import ControlledDateInput from "@/components/form/ControlledDateInput";
import { useTranslatedConstants } from "@/contexts/TranslatedConstantsContext";
import ControlledRadioButtons from "@/components/form/ControlledRadioButtons";
import { Dispatch } from "@/store";
import { useDispatch } from "react-redux";
import { router } from "expo-router";
import { format, parse } from "date-fns";
import { toast } from "sonner-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { KeyboardAvoidingView, Platform, View } from "react-native";
import { useI18n } from "@/providers/i18n/i18n.provider";

const RegisterScreen = () => {
  const { t } = useI18n();
  const constants = useTranslatedConstants();
  const { GENDERS_DATA_SHORT } = constants;
  const dispatch = useDispatch<Dispatch>();
  const [step, setStep] = useState(0);

  const registerSchema = z
    .object({
      gender: z.enum(["F", "M"]).optional(),
      username: z.string().min(1, t("auth.errors.username_required")).trim(),
      birthdate: z.string().optional(),
      phone: z
        .string()
        .max(10, t("auth.errors.phone_invalid_format"))
        .trim()
        .optional(),
      country: z
        .string()
        .trim()
        .min(1, t("auth.errors.country_required"))
        .max(2, t("auth.errors.country_invalid_format")),
      city: z.string().trim().optional(),
      email: z
        .string()
        .min(1, t("auth.errors.email_required"))
        .email(t("auth.errors.email_invalid")),
      password: z.string().min(1, t("auth.errors.password_required")),
      password_confirmation: z
        .string()
        .min(1, t("auth.errors.password_confirmation_required")),
    })
    .refine((data) => data.password === data.password_confirmation, {
      message: t("auth.errors.passwords_mismatch"),
      path: ["password_confirmation"],
    });

  type RegisterSchemaType = z.infer<typeof registerSchema>;

  const {
    // reset,
    trigger,
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<RegisterSchemaType>({
    defaultValues: {
      gender: undefined,
      username: "",
      birthdate: "",
      phone: "",
      country: "FR",
      city: "",
      email: "",
      password: "",
      password_confirmation: "",
    },
    mode: "onSubmit",
    resolver: zodResolver(registerSchema),
  });

  const handlePartialValidation = async () => {
    const isValid = await trigger(["gender", "username", "country"]);
    if (isValid) {
      setStep(1);
    }
  };

  const onSubmit = async (form: RegisterSchemaType) => {
    try {
      await dispatch.auth.register({
        ...form,
        city: form.city ?? undefined,
        phone: form.phone ?? undefined,
        birthdate: form.birthdate
          ? format(
              parse(form.birthdate!, "dd/MM/yyyy", new Date()),
              "yyyy-MM-dd",
            )
          : undefined,
      });
      toast.success(t("auth.register_success"));

      router.dismissTo("/sign-in");
    } catch (e) {
      console.error(e);
      toast.error(t("auth.errors.register_error"), {
        description: typeof e === "string" ? e : undefined,
      });
    }
  };

  return (
    <View className="flex-1 bg-background-dark">
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={{ flex: 1 }}
      >
        <SafeAreaView className="flex-1">
          <HeaderLogo />
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="h-full p-4"
          >
            <VStack space="sm">
              <BackButton
                title={`${t("auth.register")} (${step + 1}/2)`}
                onPress={step > 0 ? () => setStep(step - 1) : undefined}
              />
              <Divider />
              {step === 0 ? (
                <>
                  <ControlledRadioButtons
                    data={GENDERS_DATA_SHORT}
                    name="gender"
                    control={control}
                    title={t("auth.gender")}
                  />
                  <ControlledInput
                    name="username"
                    control={control}
                    title={t("auth.username")}
                    placeholder={t("auth.username_placeholder")}
                  />
                  <ControlledDateInput
                    name="birthdate"
                    control={control}
                    title={t("auth.birthdate")}
                    placeholder={t("auth.birthdate_placeholder")}
                  />
                  <ControlledInput
                    name="country"
                    control={control}
                    title={t("auth.country")}
                    placeholder={t("auth.country_placeholder")}
                    readOnly
                  />
                  <ControlledInput
                    name="city"
                    control={control}
                    title={t("auth.city")}
                    placeholder={t("auth.city_placeholder")}
                  />
                  <ControlledInput
                    name="phone"
                    control={control}
                    title={t("auth.phone")}
                    placeholder={t("auth.phone_placeholder")}
                  />
                </>
              ) : (
                <>
                  <ControlledInput
                    name="email"
                    control={control}
                    title={t("auth.email_address")}
                    placeholder={t("auth.email_placeholder")}
                  />
                  <ControlledPasswordInput
                    name="password"
                    control={control}
                    title={t("auth.password")}
                    placeholder={t("auth.password_placeholder")}
                  />
                  <ControlledPasswordInput
                    name="password_confirmation"
                    control={control}
                    title={t("auth.password_confirmation")}
                    placeholder={t(
                      "auth.password_confirmation_placeholder",
                    )}
                  />
                </>
              )}
              <Divider />
            </VStack>
            <HStack className="pb-[120px] mt-8">
              {step === 0 ? (
                <AppButton className="w-full" onPress={handlePartialValidation}>
                  <HStack space="sm">
                    <Text className="text-gold-ultralight text-center uppercase font-normal text-base">
                      {t("auth.next_step")}
                    </Text>
                    <ChevronRight stroke="#F2ECE0" />
                  </HStack>
                </AppButton>
              ) : (
                <GradientButton
                  className="w-full"
                  onPress={handleSubmit(onSubmit)}
                  disabled={isSubmitting}
                  loading={isSubmitting}
                >
                  {t("auth.register_button")}
                </GradientButton>
              )}
            </HStack>
          </ScrollView>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default RegisterScreen;
