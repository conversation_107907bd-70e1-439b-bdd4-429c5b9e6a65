import axios from "axios";
import { components } from "@/@types/api.types";

const axiosInstance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL,
});

axiosInstance.defaults.headers.common = { "Content-Type": "application/json" };

export const getFetcher = (url: string) =>
  axiosInstance.get(url).then((res) => res.data);

// Mock vendors for price comparison
const VENDORS = [
  "Marionnaud",
  "Sephora",
  "Nocibé",
  "Une heure pour soi",
  "Douglas",
  "Galeries Lafayette",
  "Printemps",
  "Au bon marché",
];

// Vendor URLs for linking to their websites
const VENDOR_URLS = {
  Marionnaud: "https://www.marionnaud.fr/",
  Sephora: "https://www.sephora.fr/",
  Nocibé: "https://www.nocibe.fr/",
  "Une heure pour soi": "https://www.uneheurepoursoi.com/",
  <PERSON>: "https://www.douglas.fr/",
  "Galeries Lafayette": "https://www.galerieslafayette.com/",
  Printemps: "https://www.printemps.com/",
  "Au bon marché": "https://www.24s.com/fr-fr/le-bon-marche",
};

// Function to generate a mock product link for a specific vendor
function generateVendorProductLink(
  vendorName: string,
  perfumeName: string,
  brand: string | undefined
) {
  const baseUrl =
    VENDOR_URLS[vendorName as keyof typeof VENDOR_URLS] ||
    "https://www.example.com/";
  const formattedPerfumeName = perfumeName
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^\w-]/g, "");
  const formattedBrand = brand
    ? brand
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^\w-]/g, "")
    : "brand";

  // Different vendors have different URL structures
  if (vendorName === "Sephora") {
    return `${baseUrl}search?q=${formattedBrand}+${formattedPerfumeName}`;
  } else if (vendorName === "Nocibé") {
    return `${baseUrl}parfums/c11/?product=${formattedPerfumeName}`;
  } else if (vendorName === "Marionnaud") {
    return `${baseUrl}recherche?q=${formattedBrand}%20${formattedPerfumeName}`;
  } else {
    // Generic search URL for other vendors
    return `${baseUrl}search?query=${formattedBrand}+${formattedPerfumeName}`;
  }
}

axiosInstance.interceptors.request.use(
  function (config) {
    console.info(
      `-----------${config.method?.toUpperCase()} REQUEST SENT AT "${config.baseURL}${config.url}" WITH DATA ${JSON.stringify(config.data)}-----------`
    );
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

// Mock interceptor to add vendor price data to perfume responses
// axiosInstance.interceptors.response.use(
//   function (response) {
//     // Only intercept perfume detail responses
//     if (
//       response.config.url?.includes("/perfumes/") &&
//       !response.config.url?.includes("/similar")
//     ) {
//       const perfume = response.data as components["schemas"]["Perfume"];

//       // If the perfume has variants, enhance them with vendor data
//       if (perfume && perfume.variants && Array.isArray(perfume.variants)) {
//         // Create a set of existing sizes
//         const existingSizes = new Set<number>();

//         // Collect all existing sizes
//         perfume.variants.forEach(
//           (v: components["schemas"]["PerfumeVariant"]) => {
//             if (v.size !== undefined) {
//               existingSizes.add(v.size);
//             }
//           }
//         );

//         // Add some common sizes if they don't exist
//         const commonSizes = [30, 50, 100, 200];
//         for (const size of commonSizes) {
//           if (!existingSizes.has(size)) {
//             // Add at least one variant for this size
//             perfume.variants.push({
//               id: Math.floor(Math.random() * 10000),
//               size: size,
//               price: Number((size * (0.8 + Math.random() * 0.4)).toFixed(2)), // Random price based on size, rounded to 2 decimal places
//               vendor: VENDORS[Math.floor(Math.random() * VENDORS.length)],
//               currency: "EUR",
//               in_stock: true,
//               link: generateVendorProductLink(
//                 VENDORS[Math.floor(Math.random() * VENDORS.length)],
//                 perfume.name || "",
//                 perfume.brand?.name
//               ),
//               perfume_id: perfume.id,
//               created_at: new Date().toISOString(),
//               updated_at: new Date().toISOString(),
//             });
//             existingSizes.add(size);
//           }
//         }

//         // For each size, add multiple vendor options
//         const enhancedVariants = [...perfume.variants];

//         // Convert Set to Array for iteration
//         Array.from(existingSizes).forEach((size: number) => {
//           // Add 3-6 random vendors for each size
//           const numVendors = 3 + Math.floor(Math.random() * 4);
//           const usedVendors = new Set<string>();

//           // Collect vendors already used for this size
//           perfume.variants?.forEach(
//             (v: components["schemas"]["PerfumeVariant"]) => {
//               if (v.size === size && v.vendor) {
//                 usedVendors.add(v.vendor);
//               }
//             }
//           );

//           // Add the brand as a vendor (mother house)
//           if (
//             perfume.brand &&
//             perfume.brand.name &&
//             !usedVendors.has(perfume.brand.name)
//           ) {
//             enhancedVariants.push({
//               id: Math.floor(Math.random() * 10000),
//               size: size,
//               price: Number((size * (0.9 + Math.random() * 0.3)).toFixed(2)), // Random price for the brand, rounded to 2 decimal places
//               vendor: perfume.brand.name,
//               currency: "EUR",
//               in_stock: true,
//               link: generateVendorProductLink(
//                 perfume.brand.name,
//                 perfume.name || "",
//                 perfume.brand.name
//               ),
//               perfume_id: perfume.id,
//               created_at: new Date().toISOString(),
//               updated_at: new Date().toISOString(),
//             });
//             usedVendors.add(perfume.brand.name);
//           }

//           // Add other vendors
//           for (let i = 0; i < numVendors; i++) {
//             const vendor = VENDORS[Math.floor(Math.random() * VENDORS.length)];
//             if (!usedVendors.has(vendor)) {
//               enhancedVariants.push({
//                 id: Math.floor(Math.random() * 10000),
//                 size: size,
//                 price: Number((size * (0.7 + Math.random() * 0.6)).toFixed(2)), // Random price with more variation, rounded to 2 decimal places
//                 vendor: vendor,
//                 currency: "EUR",
//                 in_stock: true,
//                 link: generateVendorProductLink(
//                   vendor,
//                   perfume.name || "",
//                   perfume.brand?.name
//                 ),
//                 perfume_id: perfume.id,
//                 created_at: new Date().toISOString(),
//                 updated_at: new Date().toISOString(),
//               });
//               usedVendors.add(vendor);
//             }
//           }
//         });

//         perfume.variants = enhancedVariants;
//       }

//       response.data = perfume;
//     }

//     return response;
//   },
//   function (error) {
//     if (error.response?.data?.errors) {
//       console.error(
//         `-----------ERROR ON REQUEST: ${error} WITH ERRORS ${JSON.stringify(error.response.data.errors)}-----------`
//       );
//       return Promise.reject(error.response.data.errors[0].message);
//     } else {
//       console.error(`-----------ERROR: ${error} -----------`);
//     }
//     return Promise.reject(error);
//   }
// );

export default axiosInstance;
