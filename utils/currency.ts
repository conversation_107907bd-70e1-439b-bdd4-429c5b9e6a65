import AsyncStorage from "@react-native-async-storage/async-storage";
import { DEFAULT_CURRENCY, SUPPORTED_CURRENCIES } from "@/constants/currencies";
import { EventEmitter } from "events";

// Create event emitter for currency changes
export const currencyEvents = new EventEmitter();
export const CURRENCY_CHANGE_EVENT = "currencyChange";

// Variable to store current currency
let currentCurrency = DEFAULT_CURRENCY;

// Load saved currency preference or use default
export const loadCurrency = async () => {
  try {
    const savedCurrency = await AsyncStorage.getItem("userCurrency");
    if (
      savedCurrency &&
      SUPPORTED_CURRENCIES.some((curr) => curr.code === savedCurrency)
    ) {
      currentCurrency = savedCurrency;
    } else {
      currentCurrency = DEFAULT_CURRENCY;
    }
    return currentCurrency;
  } catch (error) {
    console.error("Error loading currency preference:", error);
    currentCurrency = DEFAULT_CURRENCY;
    return currentCurrency;
  }
};

// Function to change currency
export const changeCurrency = async (currencyCode: string) => {
  try {
    if (SUPPORTED_CURRENCIES.some((curr) => curr.code === currencyCode)) {
      await AsyncStorage.setItem("userCurrency", currencyCode);
      currentCurrency = currencyCode;
      // Emit event when currency changes
      currencyEvents.emit(CURRENCY_CHANGE_EVENT, currencyCode);
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error saving currency preference:", error);
    return false;
  }
};

// Get current currency
export const getCurrentCurrency = () => {
  return currentCurrency;
};

// Get details about current currency
export const getCurrentCurrencyInfo = () => {
  return SUPPORTED_CURRENCIES.find((curr) => curr.code === currentCurrency);
};

// Function to convert values from EUR to current currency
export const convertPrice = (priceInEUR: number) => {
  const currency = SUPPORTED_CURRENCIES.find(
    (curr) => curr.code === currentCurrency
  );
  if (!currency) return priceInEUR; // Return original price if currency not found

  return priceInEUR * currency.rate;
};

// Function to format currency values
export const formatCurrency = (price: number) => {
  const currency = SUPPORTED_CURRENCIES.find(
    (curr) => curr.code === currentCurrency
  );
  if (!currency) return `${price.toFixed(2)} €`; // Default format

  const formattedPrice = price.toFixed(2);

  // Handle special cases
  if (currency.code === "JPY" || currency.code === "KRW") {
    return `${Math.round(price)} ${currency.symbol}`;
  }

  // Display symbol before amount for USD, GBP, etc.
  if (["USD", "GBP", "CAD", "AUD", "INR"].includes(currency.code)) {
    return `${currency.symbol}${formattedPrice}`;
  }

  // Display symbol after amount for EUR, CNY, etc.
  return `${formattedPrice} ${currency.symbol}`;
};
