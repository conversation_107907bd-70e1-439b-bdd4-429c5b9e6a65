import slugify from "slugify";

export const getNoteTranslationKey = (name: string): string => {
  // Convert French name to slug format
  const slug = slugify(name, {
    replacement: "-", // Thay thế khoảng trắng bằng dấu gạch ngang
    remove: undefined, // Không loại bỏ các ký tự, để thư viện tự xử lý
    lower: true, // Chuyển thành chữ thường
    strict: false, // Không loại bỏ hoàn toàn các ký tự đặc biệt
    locale: "fr", // Sử dụng ngôn ngữ tiếng Pháp để xử lý dấu
    trim: true, // Cắt khoảng trắng ở đầu và cuối
  });

  return `notes.${slug}`;
};

/**
 * Get the translation key for a note category
 * @param category The category name
 * @returns The translation key in the format noteCategories.{slugified-category}
 */
export const getNoteCategoryTranslationKey = (category: string): string => {
  const slug = slugify(category, {
    replacement: "-",
    lower: true,
    strict: false,
    locale: "fr",
    trim: true,
  });

  return `noteCategories.${slug}`;
};
