export const buildFilterQuery = (filters: Record<string, any>): string => {
  const queryParams: string[] = [];

  for (const [key, value] of Object.entries(filters)) {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        if (key === "notes") {
          // Special handling for notes array - join values with comma separator
          queryParams.push(`${key}=${encodeURIComponent(value.join(","))}`);
        } else {
          queryParams.push(
            `${value.map((v, index) => `${key}[${index}]=${encodeURIComponent(v)}`).join("&")}`,
          );
        }
      } else if (typeof value === "object" && "id" in value) {
        queryParams.push(`${key}=${encodeURIComponent(value.id)}`);
      } else if (typeof value === "string" || typeof value === "number") {
        queryParams.push(`${key}=${encodeURIComponent(value)}`);
      }
    }
  }

  return queryParams.length ? `?${queryParams.join("&")}` : "";
};
