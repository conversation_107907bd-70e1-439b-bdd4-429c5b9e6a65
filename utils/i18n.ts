import * as Localization from "expo-localization";
import { I18n } from "i18n-js";
import AsyncStorage from "@react-native-async-storage/async-storage";
import en from "../locales/en.json";
import fr from "../locales/fr.json";
import de from "../locales/de.json";
import fi from "../locales/fi.json";
import pl from "../locales/pl.json";
import es from "../locales/es.json";
import pt from "../locales/pt.json";
import it from "../locales/it.json";
import sv from "../locales/sv.json";
import no from "../locales/no.json";
import tr from "../locales/tr.json";
import ar from "../locales/ar.json";
import ru from "../locales/ru.json";
import uk from "../locales/uk.json";
import ja from "../locales/ja.json";
import ko from "../locales/ko.json";
import zh from "../locales/zh.json";
import hi from "../locales/hi.json";

// Import notes translations
import enNotes from "../locales/notes/en.json";
import frNotes from "../locales/notes/fr.json";
import deNotes from "../locales/notes/de.json";
import fiNotes from "../locales/notes/fi.json";
import plNotes from "../locales/notes/pl.json";
import esNotes from "../locales/notes/es.json";
import ptNotes from "../locales/notes/pt.json";
import itNotes from "../locales/notes/it.json";
import svNotes from "../locales/notes/sv.json";
import noNotes from "../locales/notes/no.json";
import trNotes from "../locales/notes/tr.json";
import arNotes from "../locales/notes/ar.json";
import ruNotes from "../locales/notes/ru.json";
import ukNotes from "../locales/notes/uk.json";
import jaNotes from "../locales/notes/ja.json";
import koNotes from "../locales/notes/ko.json";
import zhNotes from "../locales/notes/zh.json";
import hiNotes from "../locales/notes/hi.json";

// Import note categories translations
import enNoteCategories from "../locales/note-categories/en.json";
import frNoteCategories from "../locales/note-categories/fr.json";
import deNoteCategories from "../locales/note-categories/de.json";
import fiNoteCategories from "../locales/note-categories/fi.json";
import plNoteCategories from "../locales/note-categories/pl.json";
import esNoteCategories from "../locales/note-categories/es.json";
import ptNoteCategories from "../locales/note-categories/pt.json";
import itNoteCategories from "../locales/note-categories/it.json";
import svNoteCategories from "../locales/note-categories/sv.json";
import noNoteCategories from "../locales/note-categories/no.json";
import trNoteCategories from "../locales/note-categories/tr.json";
import arNoteCategories from "../locales/note-categories/ar.json";
import ruNoteCategories from "../locales/note-categories/ru.json";
import ukNoteCategories from "../locales/note-categories/uk.json";
import jaNoteCategories from "../locales/note-categories/ja.json";
import koNoteCategories from "../locales/note-categories/ko.json";
import zhNoteCategories from "../locales/note-categories/zh.json";
import hiNoteCategories from "../locales/note-categories/hi.json";

import { SUPPORTED_LANGUAGES } from "@/constants/languages";
import { EventEmitter } from "events";

// Create an event emitter for language changes
export const i18nEvents = new EventEmitter();
export const LANGUAGE_CHANGE_EVENT = "languageChange";

// Merge base translations with notes and note categories translations
const mergeTranslations = (base: any, notes: any, noteCategories: any) => ({
  ...base,
  notes: notes,
  noteCategories: noteCategories,
});

const i18n = new I18n({
  en: mergeTranslations(en, enNotes, enNoteCategories),
  fr: mergeTranslations(fr, frNotes, frNoteCategories),
  de: mergeTranslations(de, deNotes, deNoteCategories),
  fi: mergeTranslations(fi, fiNotes, fiNoteCategories),
  pl: mergeTranslations(pl, plNotes, plNoteCategories),
  es: mergeTranslations(es, esNotes, esNoteCategories),
  pt: mergeTranslations(pt, ptNotes, ptNoteCategories),
  it: mergeTranslations(it, itNotes, itNoteCategories),
  sv: mergeTranslations(sv, svNotes, svNoteCategories),
  no: mergeTranslations(no, noNotes, noNoteCategories),
  tr: mergeTranslations(tr, trNotes, trNoteCategories),
  ar: mergeTranslations(ar, arNotes, arNoteCategories),
  ru: mergeTranslations(ru, ruNotes, ruNoteCategories),
  uk: mergeTranslations(uk, ukNotes, ukNoteCategories),
  ja: mergeTranslations(ja, jaNotes, jaNoteCategories),
  ko: mergeTranslations(ko, koNotes, koNoteCategories),
  zh: mergeTranslations(zh, zhNotes, zhNoteCategories),
  hi: mergeTranslations(hi, hiNotes, hiNoteCategories),
});

i18n.defaultLocale = "fr";
i18n.enableFallback = true;

// Load saved language preference or use device language
const loadLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem("userLanguage");
    if (
      savedLanguage &&
      SUPPORTED_LANGUAGES.some((lang) => lang.code === savedLanguage)
    ) {
      i18n.locale = savedLanguage;
    } else {
      const deviceLanguage = Localization.getLocales()[0]?.languageCode;
      if (
        deviceLanguage &&
        SUPPORTED_LANGUAGES.some((lang) => lang.code === deviceLanguage)
      ) {
        i18n.locale = deviceLanguage;
      } else {
        i18n.locale = "fr";
      }
    }
  } catch (error) {
    console.error("Error loading language preference:", error);
    i18n.locale = "fr";
  }
};

// Initialize language
loadLanguage();

// Function to change language
export const changeLanguage = async (languageCode: string) => {
  try {
    await AsyncStorage.setItem("userLanguage", languageCode);
    i18n.locale = languageCode;
    // Emit an event when language changes
    i18nEvents.emit(LANGUAGE_CHANGE_EVENT, languageCode);
  } catch (error) {
    console.error("Error saving language preference:", error);
  }
};

export default i18n;
