{"cli": {"requireCommit": true, "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "preview", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com"}}, "sandbox": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "sandbox", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com"}}, "production": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com"}, "android": {"buildType": "app-bundle", "credentialsSource": "local"}}, "preview-apk": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com"}, "android": {"buildType": "apk"}}}, "submit": {"sandbox": {}, "production": {}}}