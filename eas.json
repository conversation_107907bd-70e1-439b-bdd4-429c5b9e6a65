{"cli": {"requireCommit": true, "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "preview", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com", "SENTRY_ALLOW_FAILURE": "true"}}, "sandbox": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "sandbox", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com", "SENTRY_ALLOW_FAILURE": "true"}}, "production": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com", "SENTRY_ALLOW_FAILURE": "true"}, "android": {"buildType": "app-bundle", "credentialsSource": "local"}}, "preview-apk": {"autoIncrement": true, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_API_URL": "https://api.monparfumidealmpi.com", "SENTRY_ALLOW_FAILURE": "true"}, "android": {"buildType": "apk"}}}, "submit": {"sandbox": {}, "production": {}}}