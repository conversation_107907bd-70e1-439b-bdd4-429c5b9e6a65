// https://docs.expo.dev/guides/using-eslint/
module.exports = {
  extends: ["expo", "prettier"],
  ignorePatterns: ["/dist/*", "/@types/*", "/components/ui/**/*/"],
  plugins: ["prettier", "unused-imports"],
  rules: {
    "prettier/prettier": "error",

    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "off",

    "react/display-name": "off",
    "react-hooks/exhaustive-deps": "off",

    "unused-imports/no-unused-imports": "warn",
    "unused-imports/no-unused-vars": [
      "warn",
      {
        vars: "all",
        varsIgnorePattern: "^_",
        args: "after-used",
        argsIgnorePattern: "^_",
      },
    ],
  },
};
