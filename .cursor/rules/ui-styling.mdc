# UI Styling Rules

## Core UI System
- **UI Library**: GlueStack UI v1.0+ with NativeWind (Tailwind CSS)
- **Components**: Located in [components/ui/](mdc:components/ui/)
- **Configuration**: [tailwind.config.js](mdc:tailwind.config.js), [gluestack-ui.config.json](mdc:gluestack-ui.config.json)
- **Global Styles**: [global.css](mdc:global.css)

## Component Import Pattern
- **Always** import UI components from `@/components/ui/`
- Use GlueStack UI components over native React Native components
- Extend UI components when needed for custom functionality

## GlueStack UI Components

### Component Import Pattern
Always import GlueStack UI components from the local components directory:

```typescript
// Correct imports
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Card } from '@/components/ui/card';
import { Image } from '@/components/ui/image';
import { Icon } from '@/components/ui/icon';
import { Spinner } from '@/components/ui/spinner';

// Avoid direct imports from @gluestack-ui packages
```

### Layout Components
```typescript
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Center } from '@/components/ui/center';

export const LayoutExample = () => (
  <Box className="flex-1 bg-background-0">
    <VStack space="md" className="p-4">
      <HStack space="sm" className="items-center">
        <Text>Left content</Text>
        <Text>Right content</Text>
      </HStack>
      <Center className="h-20">
        <Text>Centered content</Text>
      </Center>
    </VStack>
  </Box>
);
```

## NativeWind (Tailwind CSS) Styling

### Color System
Use the predefined color system from [tailwind.config.js](tailwind.config.js):

```typescript
// Primary colors
<Box className="bg-primary-500 text-primary-0" />

// Secondary colors  
<Box className="bg-secondary-400 text-secondary-0" />

// Semantic colors
<Box className="bg-success-500 text-success-0" />
<Box className="bg-error-500 text-error-0" />
<Box className="bg-warning-500 text-warning-0" />

// Typography colors
<Text className="text-typography-900" />
<Text className="text-typography-white" />

// Background colors
<Box className="bg-background-0" />
<Box className="bg-background-light" />
<Box className="bg-background-dark" />

// Custom colors
<Box className="bg-gold-200" />
<Box className="bg-grey-800" />
```

### Typography System
```typescript
// Font families
<Text className="font-heading-bold">Heading Text</Text>
<Text className="font-heading-semibold">Subtitle</Text>
<Text className="font-body">Body Text</Text>
<Text className="font-roboto">Regular Text</Text>

// Font sizes with Tailwind
<Text className="text-xl font-heading-bold">Large Heading</Text>
<Text className="text-lg font-heading-semibold">Medium Heading</Text>
<Text className="text-base font-body">Body Text</Text>
<Text className="text-sm font-roboto">Small Text</Text>
```

### Spacing System
```typescript
// Padding and margins
<Box className="p-4 m-2" />
<Box className="px-6 py-4" />
<Box className="mt-4 mb-2" />

// VStack/HStack spacing
<VStack space="xs" />  // 4px
<VStack space="sm" />  // 8px
<VStack space="md" />  // 16px
<VStack space="lg" />  // 24px
<VStack space="xl" />  // 32px
```

### Responsive Design
```typescript
// Responsive breakpoints
<Box className="w-full md:w-1/2 lg:w-1/3" />
<Text className="text-sm md:text-base lg:text-lg" />
<VStack space="sm md:space-md lg:space-lg" />
```

## Common Component Patterns

### Button Components
```typescript
import { Button, ButtonText } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';

export const ActionButton = ({ 
  title, 
  onPress, 
  isLoading = false, 
  variant = 'solid' 
}) => (
  <Button 
    size="md" 
    variant={variant}
    action="primary"
    onPress={onPress}
    isDisabled={isLoading}
    className="w-full"
  >
    {isLoading ? (
      <Spinner size="small" className="mr-2" />
    ) : null}
    <ButtonText>{title}</ButtonText>
  </Button>
);
```

### Card Components
```typescript
import { Card } from '@/components/ui/card';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';

export const PerfumeCard = ({ perfume }) => (
  <Card className="p-4 m-2 bg-background-0 shadow-sm">
    <VStack space="sm">
      <Text className="font-heading-semibold text-lg">
        {perfume.name}
      </Text>
      <Text className="font-body text-typography-600">
        {perfume.brand?.name}
      </Text>
    </VStack>
  </Card>
);
```

### Input Components
```typescript
import { Input, InputField } from '@/components/ui/input';
import { FormControl } from '@/components/ui/form-control';
import { Text } from '@/components/ui/text';

export const FormInput = ({ 
  label, 
  placeholder, 
  value, 
  onChangeText,
  error 
}) => (
  <FormControl isInvalid={!!error}>
    <Text className="font-body text-sm mb-1">{label}</Text>
    <Input
      variant="outline"
      size="md"
      className="bg-background-0"
    >
      <InputField
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
      />
    </Input>
    {error && (
      <Text className="text-error-500 text-xs mt-1">{error}</Text>
    )}
  </FormControl>
);
```

### Loading States
```typescript
import { Spinner } from '@/components/ui/spinner';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

export const LoadingSpinner = ({ text = 'Loading...' }) => (
  <Box className="flex-1 justify-center items-center">
    <Spinner size="large" className="mb-4" />
    <Text className="text-typography-600 font-body">{text}</Text>
  </Box>
);
```

## Dark Mode Support

### Theme-aware Components
```typescript
import { useColorScheme } from 'react-native';

export const ThemeAwareComponent = () => {
  const colorScheme = useColorScheme();
  
  return (
    <Box className={`
      ${colorScheme === 'dark' 
        ? 'bg-background-dark text-typography-white' 
        : 'bg-background-light text-typography-black'
      }
    `}>
      <Text>Theme-aware content</Text>
    </Box>
  );
};
```

### CSS Variables for Dynamic Colors
```typescript
// Colors automatically adapt to theme
<Box className="bg-background-0 text-typography-900" />
<Box className="border-outline-300" />
```

## Image Components

### Optimized Image Loading
```typescript
import { Image } from '@/components/ui/image';
import { Box } from '@/components/ui/box';

export const PerfumeImage = ({ source, alt }) => (
  <Box className="w-full h-48 rounded-lg overflow-hidden">
    <Image
      source={source}
      alt={alt}
      className="w-full h-full object-cover"
      resizeMode="cover"
    />
  </Box>
);
```

### Background Images
```typescript
import { ImageBackground } from '@/components/ui/image-background';

export const HeroSection = ({ children }) => (
  <ImageBackground
    source={require('@/assets/images/home/<USER>')}
    className="flex-1 justify-center items-center"
  >
    {children}
  </ImageBackground>
);
```

## Icon System

### Lucide Icons
```typescript
import { Icon } from '@/components/ui/icon';
import { Search, Heart, Star, User } from 'lucide-react-native';

export const IconButtons = () => (
  <HStack space="md">
    <Icon as={Search} size="xl" className="text-primary-500" />
    <Icon as={Heart} size="xl" className="text-error-500" />
    <Icon as={Star} size="xl" className="text-warning-500" />
    <Icon as={User} size="xl" className="text-secondary-500" />
  </HStack>
);
```

### Custom SVG Icons
```typescript
import { SvgXml } from 'react-native-svg';

const CustomIcon = ({ xml, size = 24, color = '#000' }) => (
  <SvgXml 
    xml={xml} 
    width={size} 
    height={size} 
    fill={color}
  />
);
```

## Animation Support

### Legend Motion Integration
```typescript
import { MotiView } from '@legendapp/motion';

export const AnimatedCard = ({ children }) => (
  <MotiView
    from={{ opacity: 0, translateY: 20 }}
    animate={{ opacity: 1, translateY: 0 }}
    transition={{ type: 'timing', duration: 300 }}
  >
    {children}
  </MotiView>
);
```

### Pressable with Haptic Feedback
```typescript
import { Pressable } from '@/components/ui/pressable';
import * as Haptics from 'expo-haptics';

export const HapticButton = ({ onPress, children }) => (
  <Pressable
    onPress={() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress?.();
    }}
    className="active:scale-95 transition-transform"
  >
    {children}
  </Pressable>
);
```

## Performance Optimization

### Lazy Loading Images
```typescript
import { useState, useEffect } from 'react';

export const LazyImage = ({ source, placeholder, ...props }) => {
  const [loaded, setLoaded] = useState(false);
  
  return (
    <Box className="relative">
      {!loaded && (
        <Box className="absolute inset-0 bg-grey-200 animate-pulse" />
      )}
      <Image
        source={source}
        onLoad={() => setLoaded(true)}
        className={`${loaded ? 'opacity-100' : 'opacity-0'} transition-opacity`}
        {...props}
      />
    </Box>
  );
};
```

### FlatList Optimization
```typescript
import { FlatList } from '@/components/ui/flat-list';

export const OptimizedList = ({ data, renderItem }) => (
  <FlatList
    data={data}
    renderItem={renderItem}
    removeClippedSubviews
    maxToRenderPerBatch={10}
    updateCellsBatchingPeriod={50}
    windowSize={10}
    getItemLayout={(data, index) => ({
      length: 100,
      offset: 100 * index,
      index,
    })}
  />
);
```

## Best Practices

1. **Use GlueStack UI components** for consistency
2. **Leverage NativeWind classes** for styling
3. **Follow the color system** defined in tailwind.config.js
4. **Use responsive design** principles
5. **Implement proper loading states** for better UX
6. **Optimize images** for performance
7. **Use animations sparingly** and purposefully
8. **Test on both light and dark themes**
9. **Follow accessibility guidelines** with proper contrast ratios
10. **Use CSS variables** for theme-aware styling

- Apply appropriate color variants for dark/light modes
- Test components in both themes
