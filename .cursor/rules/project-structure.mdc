# Project Structure & Architecture

## Overview
This is a React Native/Expo perfume discovery and comparison app called "Mon parfum idéal" (My ideal perfume). The app helps users discover, compare, and save perfumes based on various criteria.

## Tech Stack
- **Framework**: Expo SDK 53 with React Native 0.79.5
- **Language**: TypeScript 5.8.3
- **Navigation**: Expo Router v5.1+ (file-based routing)
- **UI Framework**: GlueStack UI with NativeWind (Tailwind CSS)
- **Data Fetching**: SWR for server state management
- **State Management**: Rematch (Redux toolkit alternative)
- **Internationalization**: i18n-js with 18 supported languages
- **API Types**: Auto-generated from OpenAPI specification
- **Forms**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS via NativeWind + GlueStack UI components

## Key Features
- **Perfume Discovery**: Search and browse perfumes by various criteria
- **Comparison**: Compare different perfumes side by side
- **Favorites**: Save and manage favorite perfumes
- **User Profiles**: Authentication and personalized experience
- **Multi-language**: Support for 18 languages including RTL (Arabic)
- **Responsive Design**: Optimized for mobile devices

## Directory Structure

### Core App Structure
- [`app/`] - Expo Router file-based routing
  - [`_layout.tsx`] - Root layout with providers
  - [`(app)/`] - Main app route group (authenticated)
    - [`(tabs)/`] - Tab navigation group
      - [`(home,search,favorites)/`] - Parallel route group
      - [`profile.tsx`] - Profile screen
  - Authentication screens (sign-in, register, etc.)

### Component Organization
- [`components/`] - Reusable UI components
  - [`common/`] - Generic components (Button, Input, etc.)
  - [`ui/`] - GlueStack UI component wrappers
  - [`form/`] - Form-specific components with React Hook Form
  - [`home/`] - Home screen specific components
  - [`search/`] - Search functionality components
  - [`perfumes/`] - Perfume-related components
  - [`profile/`] - Profile screen components

### State & Data Management
- [`store.ts`] - Rematch store configuration
- [`models/`] - Rematch models (auth, favorites, search)
- [`@types/api.types.ts`] - Auto-generated API types from OpenAPI
- [`api/`] - API client configuration (Axios)

### Configuration & Setup
- [`providers/`] - React context providers
  - [`swr/`] - SWR configuration
  - [`theme/`] - Theme provider
  - [`i18n/`] - Internationalization setup
  - [`currency/`] - Currency provider
- [`constants/`] - App constants and configuration
- [`locales/`] - Translation files for 18 languages

### Assets & Utilities
- [`assets/`] - Images, icons, and static assets
- [`utils/`] - Utility functions
- [`hooks/`] - Custom React hooks

## Route Structure
The app uses Expo Router with complex nested routing:

```
app/
├── _layout.tsx (Root layout)
├── sign-in.tsx (Auth screen)
├── register.tsx (Auth screen)
├── (app)/
│   ├── _layout.tsx (App layout)
│   └── (tabs)/
│       ├── _layout.tsx (Tab layout)
│       ├── (home,search,favorites)/
│       │   ├── _layout.tsx (Parallel route layout)
│       │   ├── home/index.tsx
│       │   ├── search/index.tsx
│       │   ├── search/results.tsx
│       │   ├── search/perfume/[id].tsx
│       │   └── favorites/index.tsx
│       └── profile.tsx
```

## Key Files Reference
- [`package.json`] - Project dependencies and scripts
- [`app.json`] - Expo configuration
- [`tailwind.config.js`] - Tailwind CSS configuration
- [`gluestack-ui.config.json`] - GlueStack UI configuration
- [`babel.config.js`] - Babel configuration with module resolver
- [`metro.config.js`] - Metro bundler configuration

## Development Guidelines
1. Always use TypeScript with strict type checking
2. Follow the established component structure patterns
3. Use auto-generated API types from [`@types/api.types.ts`]
4. Implement proper internationalization using i18n-js
5. Follow the established styling patterns with GlueStack UI + NativeWind
6. Use SWR for all data fetching operations
7. Implement proper error handling and loading states
8. Follow accessibility best practices
