# Internationalization (i18n)

## Language Support
The app supports 17 languages with full translation files in [locales/](locales/):
- English (en)
- French (fr) 
- Spanish (es)
- German (de)
- Italian (it)
- Portuguese (pt)
- Russian (ru)
- Arabic (ar) - RTL support
- Japanese (ja)
- Korean (ko)
- Chinese (zh)
- Hindi (hi)
- Finnish (fi)
- Swedish (sv)
- Norwegian (no)
- Polish (pl)
- Turkish (tr)
- Ukrainian (uk)

## i18n-js Usage Patterns

### Basic Translation Usage
```typescript
import { useI18n } from '@/providers/i18n/i18n.provider';

export const Component = () => {
  const { t, locale } = useI18n();

  return (
    <View>
      <Text>{t('common.loading')}</Text>
      <Text>{t('home.find_ideal_perfume_button')}</Text>
    </View>
  );
};
```

### Translation with Interpolation
```typescript
const { t } = useI18n();

// Using interpolation
const message = t('search.no_results', { query: '<PERSON><PERSON>' });
// Result: "No results for the search: "<PERSON><PERSON>""

// Using parameters
const welcome = t('profile.hello', { name: '<PERSON>' });
```

### Pluralization
```typescript
const { t } = useI18n();

// Pluralization handling
const resultsText = t('search.results_count', { 
  count: perfumes.length,
  defaultValue: perfumes.length === 1 ? 'result' : 'results'
});
```

### Locale-Specific Logic
```typescript
import { useMemo } from 'react';
import { useI18n } from '@/providers/i18n/i18n.provider';

export const LocalizedComponent = () => {
  const { locale } = useI18n();

  const isRTL = useMemo(() => {
    return locale.startsWith('ar');
  }, [locale]);

  const isFrench = useMemo(() => {
    return locale.startsWith('fr');
  }, [locale]);

  const backgroundImage = useMemo(() => {
    return isFrench 
      ? require('@/assets/images/home/<USER>')
      : require('@/assets/images/home/<USER>');
  }, [isFrench]);

  return (
    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
      <ImageBackground source={backgroundImage}>
        {/* Content */}
      </ImageBackground>
    </View>
  );
};
```

## Translation Key Structure

### Naming Convention
Translation keys follow a hierarchical structure:
- `section.subsection.key`
- Use snake_case for keys
- Use descriptive names that indicate context

### Common Key Categories
```typescript
// Layout navigation
t('layout.home')
t('layout.search')
t('layout.favorites')
t('layout.profile')

// Common terms
t('common.loading')
t('common.error')
t('common.save')
t('common.cancel')

// Authentication
t('auth.login')
t('auth.register')
t('auth.forgot_password')

// Search functionality
t('search.by_perfume')
t('search.by_criterias')
t('search.no_results')

// Perfume details
t('perfume.composition_title')
t('perfume.head_notes_title')
t('perfume.heart_notes_title')
t('perfume.base_notes_title')
```

### Translation Files Structure
Each language file follows the same structure:
```json
{
  "layout": {
    "home": "Home",
    "search": "Search",
    "favorites": "Favorites",
    "profile": "Profile"
  },
  "common": {
    "genders": {
      "female": "Female",
      "male": "Male",
      "mixed": "Mixed"
    },
    "families": {
      "hesperides": "Hesperides",
      "floral": "Floral",
      "woody": "Woody"
    }
  }
}
```

## Specialized Translation Files

### Notes and Categories
The app has dedicated translation files for perfume notes and categories:
- [locales/notes/](locales/notes/) - Perfume note translations
- [locales/note-categories/](locales/note-categories/) - Note category translations

```typescript
import { useTranslatedConstants } from '@/hooks/useTranslatedConstants';

export const NoteComponent = ({ note }: { note: Note }) => {
  const { getTranslatedNote } = useTranslatedConstants();
  
  return (
    <Text>{getTranslatedNote(note.name)}</Text>
  );
};
```

### Dynamic Translation Loading
```typescript
import { useI18n } from '@/providers/i18n/i18n.provider';

export const useDynamicTranslations = () => {
  const { locale } = useI18n();

  const loadNoteTranslations = useCallback(async () => {
    const notes = await import(`@/locales/notes/${locale}.json`);
    return notes.default;
  }, [locale]);

  return { loadNoteTranslations };
};
```

## RTL Support

### Arabic Language Support
```typescript
import { I18nManager } from 'react-native';

export const useRTLSupport = () => {
  const { locale } = useI18n();
  
  const isRTL = useMemo(() => {
    return locale.startsWith('ar');
  }, [locale]);

  useEffect(() => {
    if (isRTL !== I18nManager.isRTL) {
      I18nManager.forceRTL(isRTL);
      // Restart app to apply RTL changes
    }
  }, [isRTL]);

  return { isRTL };
};
```

### RTL-Aware Styles
```typescript
import { StyleSheet } from 'react-native';
import { useI18n } from '@/providers/i18n/i18n.provider';

export const RTLAwareComponent = () => {
  const { locale } = useI18n();
  const isRTL = locale.startsWith('ar');

  const styles = StyleSheet.create({
    container: {
      flexDirection: isRTL ? 'row-reverse' : 'row',
      textAlign: isRTL ? 'right' : 'left',
    },
  });

  return <View style={styles.container}>{/* Content */}</View>;
};
```

## Performance Considerations

### Lazy Loading Translations
```typescript
const { locale } = useI18n();

// Only load translations when needed
const translations = useMemo(() => {
  return require(`@/locales/${locale}.json`);
}, [locale]);
```

### Translation Caching
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

const TRANSLATION_CACHE_KEY = 'translations_cache';

export const cacheTranslations = async (locale: string, translations: object) => {
  await AsyncStorage.setItem(
    `${TRANSLATION_CACHE_KEY}_${locale}`,
    JSON.stringify(translations)
  );
};

export const getCachedTranslations = async (locale: string) => {
  const cached = await AsyncStorage.getItem(`${TRANSLATION_CACHE_KEY}_${locale}`);
  return cached ? JSON.parse(cached) : null;
};
```

## Translation Validation

### Missing Translation Fallbacks
```typescript
const { t } = useI18n();

// Always provide fallbacks for missing translations
const text = t('some.key', { defaultValue: 'Default Text' });

// Or use interpolation with fallbacks
const textWithFallback = t('missing.key') || 'Fallback text';
```

### Translation Testing
```typescript
// Test component with different locales
import { I18nProvider } from '@/providers/i18n/i18n.provider';

export const TestWithLocale = ({ locale, children }) => (
  <I18nProvider initialLocale={locale}>
    {children}
  </I18nProvider>
);
```

## Best Practices

1. **Always use the `useI18n` hook** instead of importing i18n directly
2. **Provide meaningful default values** for translations
3. **Use interpolation** for dynamic content instead of string concatenation
4. **Keep translation keys descriptive** and hierarchical
5. **Test RTL layouts** when adding new components
6. **Use specialized translation files** for domain-specific terms (notes, categories)
7. **Cache translations** for better performance
8. **Validate translations** exist before using them

## Common Patterns

### Form Validation Messages
```typescript
const { t } = useI18n();

const validationSchema = {
  email: {
    required: t('auth.errors.email_required'),
    invalid: t('auth.errors.email_invalid'),
  },
  password: {
    required: t('auth.errors.password_required'),
  },
};
```

### Dynamic Content Translation
```typescript
const { t, locale } = useI18n();

const getPerfumeFamilyName = (family: string) => {
  return t(`common.families.${family}`, { defaultValue: family });
};

const getGenderName = (gender: string) => {
  return t(`common.genders.${gender.toLowerCase()}`, { defaultValue: gender });
};
```

- Avoid unnecessary re-renders when locale changes
- Cache translated strings when appropriate
