---
title: "TypeScript React Native Components"
description: "Component patterns, props interfaces, and React Native best practices"
applies_to: ["*.ts", "*.tsx"]
---

# TypeScript React Native Components

## Component Structure
Always follow this component structure pattern:

```typescript
import React, { memo, useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { components } from '@/types/api.types';

// Props interface with proper typing
interface ComponentNameProps {
  // Always use specific types from API types when possible
  perfume: components['schemas']['Perfume'];
  onPress?: (id: number) => void;
  isLoading?: boolean;
  children?: React.ReactNode;
}

// Use memo for performance optimization
const ComponentName = memo<ComponentNameProps>(({
  perfume,
  onPress,
  isLoading = false,
  children,
}) => {
  // Use useCallback for event handlers
  const handlePress = useCallback(() => {
    onPress?.(perfume.id!);
  }, [onPress, perfume.id]);

  // Use useMemo for expensive calculations
  const displayName = useMemo(() => {
    return perfume.display_name || perfume.name;
  }, [perfume.display_name, perfume.name]);

  return (
    <View>
      {/* Component JSX */}
    </View>
  );
});

ComponentName.displayName = 'ComponentName';

export default ComponentName;
```

## Props Interface Patterns

### API Data Props
Always use types from `@types/api.types.ts`:

```typescript
import { components } from '@/types/api.types';

interface PerfumeCardProps {
  perfume: components['schemas']['Perfume'];
  brand?: components['schemas']['Brand'];
  onFavorite?: (perfumeId: number) => void;
  showPrice?: boolean;
}
```

### Event Handler Props
Use specific callback types:

```typescript
interface SearchFormProps {
  onSearch: (query: string) => void;
  onFilterChange: (filters: SearchFilters) => void;
  onReset: () => void;
}
```

### Loading & Error States
Always include loading and error handling:

```typescript
interface DataComponentProps {
  isLoading?: boolean;
  error?: Error | null;
  data?: components['schemas']['Perfume'][];
  onRetry?: () => void;
}
```

## React Native Specific Patterns

### Performance Optimization
- Use `memo` for components that receive stable props
- Use `useCallback` for event handlers
- Use `useMemo` for expensive calculations
- Use `FlatList` for large lists, never `ScrollView` with `map`

### Touch Handling
```typescript
import { Pressable, GestureResponderEvent } from 'react-native';

interface TouchableComponentProps {
  onPress: (event: GestureResponderEvent) => void;
  disabled?: boolean;
  children: React.ReactNode;
}

const TouchableComponent = ({ onPress, disabled, children }: TouchableComponentProps) => (
  <Pressable
    onPress={onPress}
    disabled={disabled}
    style={({ pressed }) => ({
      opacity: pressed ? 0.6 : 1,
    })}
  >
    {children}
  </Pressable>
);
```

## Custom Hooks Pattern
Create reusable hooks for common functionality:

```typescript
import { useCallback, useState } from 'react';

interface UseToggleReturn {
  isOpen: boolean;
  toggle: () => void;
  open: () => void;
  close: () => void;
}

export const useToggle = (initialState = false): UseToggleReturn => {
  const [isOpen, setIsOpen] = useState(initialState);

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  return { isOpen, toggle, open, close };
};
```

## Error Handling
Implement proper error boundaries and error states:

```typescript
interface ErrorComponentProps {
  error: Error;
  onRetry?: () => void;
}

const ErrorComponent: React.FC<ErrorComponentProps> = ({ error, onRetry }) => (
  <View className="flex-1 justify-center items-center p-4">
    <Text className="text-red-500 text-center mb-4">
      Something went wrong
    </Text>
    {onRetry && (
      <Pressable onPress={onRetry} className="bg-blue-500 px-4 py-2 rounded">
        <Text className="text-white">Try Again</Text>
      </Pressable>
    )}
  </View>
);
```

## Accessibility
Always implement proper accessibility:

```typescript
<Pressable
  accessibilityRole="button"
  accessibilityLabel={`Add ${perfume.name} to favorites`}
  accessibilityHint="Double tap to add this perfume to your favorites"
  onPress={handleFavorite}
>
  <Text>{perfume.name}</Text>
</Pressable>
```

## Component Organization
- Keep components focused on a single responsibility
- Extract complex logic into custom hooks
- Use composition over inheritance
- Prefer function components over class components
- Use TypeScript strict mode features

## Example: Complete Component
```typescript
import React, { memo, useCallback } from 'react';
import { Pressable } from 'react-native';
import { Box, Text, Image } from '@/components/ui';
import { components } from '@/types/api.types';

interface PerfumeCardProps {
  perfume: components['schemas']['Perfume'];
  onPress: (perfumeId: number) => void;
  onFavorite: (perfumeId: number) => void;
  isFavorite: boolean;
}

const PerfumeCard = memo<PerfumeCardProps>(({
  perfume,
  onPress,
  onFavorite,
  isFavorite,
}) => {
  const handlePress = useCallback(() => {
    onPress(perfume.id!);
  }, [onPress, perfume.id]);

  const handleFavorite = useCallback(() => {
    onFavorite(perfume.id!);
  }, [onFavorite, perfume.id]);

  return (
    <Pressable
      onPress={handlePress}
      accessibilityRole="button"
      accessibilityLabel={`View details for ${perfume.name}`}
      className="bg-white rounded-lg shadow-sm p-4 mb-4"
    >
      <Box className="flex-row items-center space-x-4">
        <Image
          source={{ uri: perfume.picture?.url }}
          className="w-16 h-16 rounded-lg"
          alt={perfume.name}
        />
        <Box className="flex-1">
          <Text className="font-semibold text-gray-900">
            {perfume.display_name || perfume.name}
          </Text>
          <Text className="text-sm text-gray-600">
            {perfume.brand?.display_name || perfume.brand?.name}
          </Text>
        </Box>
        <Pressable
          onPress={handleFavorite}
          accessibilityRole="button"
          accessibilityLabel={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          className="p-2"
        >
          <HeartIcon filled={isFavorite} />
        </Pressable>
      </Box>
    </Pressable>
  );
});

PerfumeCard.displayName = 'PerfumeCard';

export default PerfumeCard;
```
