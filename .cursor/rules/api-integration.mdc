# API Integration Rules

## SWR Data Fetching
- Use SWR for all API data fetching
- Configure SWR with proper error handling and loading states
- Use SWR's built-in caching and revalidation features
- Import from `swr` package as used in [components/search/BrandList.tsx](mdc:components/search/BrandList.tsx)

## API Types
- **Always** use auto-generated types from [@types/api.types.ts](mdc:@types/api.types.ts)
- Import types as: `import { components } from "@/@types/api.types"`
- Use schema types: `components["schemas"]["Brand"]`, `components["schemas"]["Perfume"]`, etc.
- **Never** create manual API response types - use the generated ones

## SWR Hook Patterns
```typescript
import useSWR from "swr";
import { components } from "@/@types/api.types";

// Basic data fetching
const { data, isLoading, error } = useSWR<components["schemas"]["Brand"][]>(
  "/brands?all=1",
  { keepPreviousData: false }
);

// With error handling
const { data: perfumes, isLoading, error } = useSWR<components["schemas"]["Perfume"][]>(
  "/perfumes",
  { 
    keepPreviousData: true,
    revalidateOnFocus: false 
  }
);
```

## Common API Patterns

### Perfume Search
```typescript
import useSWR from 'swr';
import { components } from '@/@types/api.types';

interface SearchParams {
  query?: string;
  family?: string;
  gender?: string;
  brand?: string;
  notes?: string[];
}

export const usePerfumeSearch = (params: SearchParams) => {
  const searchQuery = new URLSearchParams({
    ...(params.query && { q: params.query }),
    ...(params.family && { family: params.family }),
    ...(params.gender && { gender: params.gender }),
    ...(params.brand && { brand: params.brand }),
    ...(params.notes?.length && { notes: params.notes.join(',') })
  }).toString();

  const { data, isLoading, error } = useSWR<components["schemas"]["Perfume"][]>(
    searchQuery ? `/perfumes?${searchQuery}` : null,
    { keepPreviousData: true }
  );

  return { perfumes: data, isLoading, error };
};
```

### Perfume Details
```typescript
export const usePerfumeDetails = (id: string) => {
  const { data, isLoading, error } = useSWR<components["schemas"]["Perfume"]>(
    id ? `/perfumes/${id}` : null,
    { revalidateOnFocus: false }
  );

  return { perfume: data, isLoading, error };
};
```

### Similar Perfumes
```typescript
export const useSimilarPerfumes = (perfumeId: string) => {
  const { data, isLoading, error } = useSWR<components["schemas"]["Perfume"][]>(
    perfumeId ? `/perfumes/${perfumeId}/similar` : null,
    { keepPreviousData: true }
  );

  return { similarPerfumes: data, isLoading, error };
};
```

### Brands List
```typescript
export const useBrands = () => {
  const { data, isLoading, error } = useSWR<components["schemas"]["Brand"][]>(
    "/brands",
    { revalidateOnFocus: false }
  );

  return { brands: data, isLoading, error };
};
```

### Notes by Category
```typescript
export const useNotes = (categoryId?: number) => {
  const { data, isLoading, error } = useSWR<components["schemas"]["Note"][]>(
    categoryId ? `/notes?category=${categoryId}` : "/notes",
    { keepPreviousData: true }
  );

  return { notes: data, isLoading, error };
};
```

## API Configuration
- Use the configured axios instance from [api/axios.ts](mdc:api/axios.ts)
- Base URL and common headers are configured centrally
- Handle authentication tokens through axios interceptors

## Error Handling
- Always handle `isLoading` and `error` states
- Show appropriate loading spinners using [components/ui/spinner](mdc:components/ui/spinner)
- Display user-friendly error messages
- Use fallback UI for failed requests

## Data Validation
- Trust the OpenAPI generated types for response structure
- Use TypeScript's strict mode for type checking
- Validate critical data before using in components
- Handle null/undefined values gracefully

## Caching Strategy
- Use SWR's default caching behavior
- Configure `keepPreviousData` for smooth UX
- Set appropriate `revalidateOnFocus` based on data freshness needs
- Use mutate() for manual cache updates after mutations

## Authentication Patterns
```typescript
// User profile (authenticated)
export const useProfile = () => {
  const { data, isLoading, error } = useSWR<components["schemas"]["User"]>(
    "/profile",
    { revalidateOnFocus: false }
  );

  return { user: data, isLoading, error };
};

// Handle authentication errors
export const useAuthenticatedSWR = <T>(url: string, options?: any) => {
  const { data, isLoading, error } = useSWR<T>(url, {
    ...options,
    onError: (error) => {
      if (error.response?.status === 401) {
        // Handle authentication error
        router.push('/sign-in');
      }
    }
  });

  return { data, isLoading, error };
};
```

## Mutations & Updates
```typescript
import { mutate } from 'swr';

// Update favorites
export const toggleFavorite = async (perfumeId: string) => {
  try {
    await apiClient.post(`/favorites/${perfumeId}`);
    // Update cache
    mutate('/favorites');
    mutate(`/perfumes/${perfumeId}`);
  } catch (error) {
    // Handle error
  }
};

// Update profile
export const updateProfile = async (userData: Partial<components["schemas"]["User"]>) => {
  try {
    const response = await apiClient.put('/profile', userData);
    // Update cache
    mutate('/profile');
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## API Endpoints
Key endpoints as defined in [@types/api.types.ts](mdc:@types/api.types.ts):
- `/brands` - List all brands
- `/perfumes` - List perfumes with filters
- `/perfumes/{id}` - Get specific perfume details
- `/perfumes/{id}/similar` - Get similar perfumes
- `/notes` - Get perfume notes
- `/auth/*` - Authentication endpoints
- `/profile` - User profile management

## Performance Optimization
- Use `keepPreviousData: true` for search results
- Implement pagination for large datasets
- Use `revalidateOnFocus: false` for static data
- Implement proper cache invalidation strategies
- Use conditional fetching with SWR's conditional pattern

## Example Implementation
```typescript
import useSWR from "swr";
import { components } from "@/@types/api.types";
import { Spinner } from "@/components/ui/spinner";
import { Text } from "@/components/ui/text";

export function PerfumeList() {
  const { data: perfumes, isLoading, error } = useSWR<components["schemas"]["Perfume"][]>(
    "/perfumes",
    { keepPreviousData: true }
  );

  if (isLoading) return <Spinner />;
  if (error) return <Text>Error loading perfumes</Text>;
  if (!perfumes) return <Text>No perfumes found</Text>;

  return (
    // Render perfumes...
  );
}
```

# API Integration

## SWR Configuration and Usage

### Base SWR Setup
The app uses SWR for data fetching with automatic caching and revalidation. All API calls should use the configured SWR instance from [providers/swr/swr.provider.tsx](providers/swr/swr.provider.tsx).

### Auto-Generated API Types
**MANDATORY**: Always use auto-generated types from [@types/api.types.ts](@types/api.types.ts):

```typescript
import { components, paths } from '@/types/api.types';

// Schema types
type Perfume = components['schemas']['Perfume'];
type Brand = components['schemas']['Brand'];
type Note = components['schemas']['Note'];
type User = components['schemas']['User'];

// Response types
type PerfumesResponse = paths['/perfumes']['get']['responses']['200']['content']['application/json'];
type PerfumeResponse = paths['/perfumes/{id}']['get']['responses']['200']['content']['application/json'];
```

### Data Fetching Patterns

#### Basic SWR Usage
```typescript
import useSWR from 'swr';
import { axiosInstance } from '@/api/axios';
import { components } from '@/types/api.types';

type Perfume = components['schemas']['Perfume'];

const fetcher = (url: string) => axiosInstance.get(url).then(res => res.data);

export const usePerfumes = () => {
  const { data, error, isLoading, mutate } = useSWR<Perfume[]>(
    '/perfumes',
    fetcher
  );

  return {
    perfumes: data,
    isLoading,
    error,
    refetch: mutate
  };
};
```

#### Parameterized Queries
```typescript
export const usePerfume = (id: string | null) => {
  const { data, error, isLoading } = useSWR<Perfume>(
    id ? `/perfumes/${id}` : null,
    fetcher
  );

  return {
    perfume: data,
    isLoading,
    error
  };
};
```

#### Search with Debouncing
```typescript
import { useDebounce } from 'use-debounce';

export const useSearchPerfumes = (query: string) => {
  const [debouncedQuery] = useDebounce(query, 300);
  
  const { data, error, isLoading } = useSWR<Perfume[]>(
    debouncedQuery ? `/perfumes?search=${encodeURIComponent(debouncedQuery)}` : null,
    fetcher
  );

  return {
    perfumes: data || [],
    isLoading,
    error
  };
};
```

### Error Handling

#### Global Error Handling
```typescript
import { toast } from 'sonner-native';

const handleError = (error: any) => {
  console.error('API Error:', error);
  
  if (error.response?.status === 401) {
    // Handle authentication error
    toast.error('Please log in to continue');
  } else if (error.response?.status === 404) {
    toast.error('Resource not found');
  } else {
    toast.error('An error occurred. Please try again.');
  }
};

export const usePerfumesWithErrorHandling = () => {
  const { data, error, isLoading } = useSWR<Perfume[]>('/perfumes', fetcher);
  
  useEffect(() => {
    if (error) {
      handleError(error);
    }
  }, [error]);

  return { perfumes: data, isLoading, error };
};
```

### Mutations and Optimistic Updates

#### POST/PUT Operations
```typescript
import { mutate } from 'swr';

export const useCreatePerfume = () => {
  const createPerfume = async (perfumeData: Partial<Perfume>) => {
    try {
      const response = await axiosInstance.post('/perfumes', perfumeData);
      
      // Revalidate perfumes list
      mutate('/perfumes');
      
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };

  return { createPerfume };
};
```

#### Optimistic Updates
```typescript
export const useToggleFavorite = () => {
  const toggleFavorite = async (perfumeId: string) => {
    try {
      // Optimistic update
      mutate(
        '/favorites',
        (favorites: Perfume[]) => {
          const exists = favorites.some(p => p.id === perfumeId);
          if (exists) {
            return favorites.filter(p => p.id !== perfumeId);
          } else {
            return [...favorites, { id: perfumeId } as Perfume];
          }
        },
        false
      );

      // Actual API call
      await axiosInstance.post(`/favorites/${perfumeId}`);
      
      // Revalidate
      mutate('/favorites');
    } catch (error) {
      // Revert on error
      mutate('/favorites');
      handleError(error);
    }
  };

  return { toggleFavorite };
};
```

### Caching Strategies

#### Cache Configuration
```typescript
const SWRConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 5 * 60 * 1000, // 5 minutes
  dedupingInterval: 2000,
  errorRetryCount: 3,
  errorRetryInterval: 1000,
};
```

#### Manual Cache Management
```typescript
import { mutate, cache } from 'swr';

// Clear specific cache
mutate('/perfumes');

// Clear all cache
cache.clear();

// Prefetch data
mutate('/perfumes', fetcher('/perfumes'));
```

### Authentication Headers

#### Axios Configuration
```typescript
// In api/axios.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

axiosInstance.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Pagination Support

#### Cursor-based Pagination
```typescript
export const usePaginatedPerfumes = (limit = 20) => {
  const [cursor, setCursor] = useState<string | null>(null);
  
  const { data, error, isLoading } = useSWR<PaginatedResponse<Perfume>>(
    `/perfumes?limit=${limit}${cursor ? `&cursor=${cursor}` : ''}`,
    fetcher
  );

  const loadMore = useCallback(() => {
    if (data?.nextCursor) {
      setCursor(data.nextCursor);
    }
  }, [data?.nextCursor]);

  return {
    perfumes: data?.items || [],
    isLoading,
    error,
    hasMore: !!data?.nextCursor,
    loadMore
  };
};
```

### Offline Support

#### Cache Persistence
```typescript
import { unstable_serialize } from 'swr';

// Save to AsyncStorage
const saveToStorage = async (key: string, data: any) => {
  await AsyncStorage.setItem(key, JSON.stringify(data));
};

// Load from AsyncStorage
const loadFromStorage = async (key: string) => {
  const data = await AsyncStorage.getItem(key);
  return data ? JSON.parse(data) : null;
};
```

### API Endpoints Documentation

#### Common Endpoints
- `GET /perfumes` - List all perfumes
- `GET /perfumes/{id}` - Get single perfume
- `GET /perfumes/{id}/similar` - Get similar perfumes
- `GET /brands` - List all brands
- `GET /notes` - List all notes
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /profile` - User profile
- `PUT /profile` - Update user profile

#### Query Parameters
- `?search=query` - Search perfumes
- `?family=woody` - Filter by olfactory family
- `?gender=M` - Filter by gender
- `?limit=20&offset=0` - Pagination

# API Integration with SWR

## Overview
All API integration must use SWR for data fetching and auto-generated types from `@types/api.types.ts`. Never create manual API type definitions.

## SWR Configuration
The app uses SWR provider configured in `providers/swr/swr.provider.tsx`:

```typescript
import useSWR from 'swr';
import { components } from '@/types/api.types';

// Example data fetching
const { data, error, isLoading, mutate } = useSWR<components['schemas']['Perfume'][]>(
  '/perfumes',
  {
    keepPreviousData: true,
    revalidateOnFocus: false,
  }
);
```

## API Types Usage

### Always Use Auto-Generated Types
```typescript
import { components } from '@/types/api.types';

// Use these types for all API responses
type Perfume = components['schemas']['Perfume'];
type Brand = components['schemas']['Brand'];
type Note = components['schemas']['Note'];
type User = components['schemas']['User'];
type PerfumeVariant = components['schemas']['PerfumeVariant'];
type PaginationMeta = components['schemas']['PaginationMeta'];
```

### API Response Patterns
```typescript
// Single resource
interface PerfumeResponse {
  data: components['schemas']['Perfume'];
}

// Collection with pagination
interface PerfumesResponse {
  data: components['schemas']['Perfume'][];
  meta: components['schemas']['PaginationMeta'];
}
```

## Data Fetching Patterns

### Basic Data Fetching
```typescript
import useSWR from 'swr';
import { components } from '@/types/api.types';

export const usePerfumes = () => {
  const { data, error, isLoading } = useSWR<components['schemas']['Perfume'][]>(
    '/perfumes',
    {
      keepPreviousData: true,
      revalidateOnFocus: false,
    }
  );

  return {
    perfumes: data || [],
    isLoading,
    error,
  };
};
```

### Parametrized Queries
```typescript
export const usePerfumeSearch = (query: string, filters?: SearchFilters) => {
  const queryParams = new URLSearchParams();
  if (query) queryParams.append('search', query);
  if (filters?.brand) queryParams.append('brand', filters.brand);
  if (filters?.gender) queryParams.append('gender', filters.gender);

  const { data, error, isLoading } = useSWR<components['schemas']['Perfume'][]>(
    query || filters ? `/perfumes?${queryParams.toString()}` : null,
    {
      keepPreviousData: true,
      dedupingInterval: 5000,
    }
  );

  return {
    perfumes: data || [],
    isLoading,
    error,
  };
};
```

### Single Resource Fetching
```typescript
export const usePerfume = (id: number | string) => {
  const { data, error, isLoading } = useSWR<components['schemas']['Perfume']>(
    id ? `/perfumes/${id}` : null,
    {
      revalidateOnFocus: false,
    }
  );

  return {
    perfume: data,
    isLoading,
    error,
  };
};
```

### Similar Perfumes
```typescript
export const useSimilarPerfumes = (perfumeId: number) => {
  const { data, error, isLoading } = useSWR<components['schemas']['Perfume'][]>(
    perfumeId ? `/perfumes/${perfumeId}/similar` : null,
    {
      revalidateOnFocus: false,
    }
  );

  return {
    similarPerfumes: data || [],
    isLoading,
    error,
  };
};
```

## Error Handling

### Standard Error Handling Pattern
```typescript
import useSWR from 'swr';
import { components } from '@/types/api.types';

export const usePerfumesWithError = () => {
  const { data, error, isLoading, mutate } = useSWR<components['schemas']['Perfume'][]>(
    '/perfumes'
  );

  const retry = () => {
    mutate();
  };

  return {
    perfumes: data || [],
    isLoading,
    error,
    retry,
    isEmpty: !isLoading && !error && data?.length === 0,
  };
};
```

### Component Error Handling
```typescript
const PerfumeList = () => {
  const { perfumes, isLoading, error, retry } = usePerfumesWithError();

  if (isLoading) return <Spinner />;
  if (error) return <ErrorComponent error={error} onRetry={retry} />;
  if (isEmpty) return <EmptyState />;

  return (
    <FlatList
      data={perfumes}
      renderItem={({ item }) => <PerfumeCard perfume={item} />}
      keyExtractor={(item) => item.id?.toString() || ''}
    />
  );
};
```

## Mutations and Updates

### Optimistic Updates
```typescript
import { mutate } from 'swr';
import { components } from '@/types/api.types';

export const useFavorites = () => {
  const { data, error, isLoading } = useSWR<components['schemas']['Perfume'][]>('/favorites');

  const addToFavorites = async (perfume: components['schemas']['Perfume']) => {
    // Optimistic update
    const updatedFavorites = [...(data || []), perfume];
    mutate('/favorites', updatedFavorites, false);

    try {
      await fetch('/favorites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ perfume_id: perfume.id }),
      });
      
      // Revalidate
      mutate('/favorites');
    } catch (error) {
      // Revert on error
      mutate('/favorites');
      throw error;
    }
  };

  const removeFromFavorites = async (perfumeId: number) => {
    const updatedFavorites = data?.filter(p => p.id !== perfumeId) || [];
    mutate('/favorites', updatedFavorites, false);

    try {
      await fetch(`/favorites/${perfumeId}`, { method: 'DELETE' });
      mutate('/favorites');
    } catch (error) {
      mutate('/favorites');
      throw error;
    }
  };

  return {
    favorites: data || [],
    isLoading,
    error,
    addToFavorites,
    removeFromFavorites,
  };
};
```

## Caching Strategy

### Configure SWR Options
```typescript
// For frequently changing data
const { data } = useSWR('/trending-perfumes', {
  refreshInterval: 60000, // 1 minute
  revalidateOnFocus: true,
});

// For static data
const { data } = useSWR('/brands', {
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
  refreshInterval: 0,
});

// For user-specific data
const { data } = useSWR('/profile', {
  revalidateOnFocus: true,
  errorRetryCount: 3,
});
```

## Component Integration Example

```typescript
import React, { memo, useCallback } from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { Box, Spinner, Text } from '@/components/ui';
import { components } from '@/types/api.types';
import useSWR from 'swr';

interface PerfumeListProps {
  searchQuery?: string;
  filters?: SearchFilters;
}

const PerfumeList = memo<PerfumeListProps>(({ searchQuery, filters }) => {
  const { data, error, isLoading, mutate } = useSWR<components['schemas']['Perfume'][]>(
    searchQuery || filters ? `/perfumes?search=${searchQuery}` : '/perfumes',
    {
      keepPreviousData: true,
      revalidateOnFocus: false,
    }
  );

  const handleRefresh = useCallback(() => {
    mutate();
  }, [mutate]);

  const renderPerfume = useCallback(({ item }: { item: components['schemas']['Perfume'] }) => (
    <PerfumeCard perfume={item} />
  ), []);

  if (isLoading && !data) {
    return (
      <Box className="flex-1 justify-center items-center">
        <Spinner />
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="flex-1 justify-center items-center p-4">
        <Text className="text-red-500 text-center mb-4">
          Failed to load perfumes
        </Text>
        <Button onPress={handleRefresh} title="Retry" />
      </Box>
    );
  }

  return (
    <FlatList
      data={data || []}
      renderItem={renderPerfume}
      keyExtractor={(item) => item.id?.toString() || ''}
      refreshControl={
        <RefreshControl
          refreshing={isLoading}
          onRefresh={handleRefresh}
        />
      }
      showsVerticalScrollIndicator={false}
    />
  );
});

PerfumeList.displayName = 'PerfumeList';

export default PerfumeList;
```

## Key Endpoints Reference

Based on the API types, these are the main endpoints:

- `GET /perfumes` - List all perfumes
- `GET /perfumes/{id}` - Get single perfume
- `GET /perfumes/{id}/similar` - Get similar perfumes
- `GET /brands` - List all brands
- `GET /notes` - List all notes
- `GET /profile` - Get user profile
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/logout` - User logout

## Best Practices

1. **Always use auto-generated types** from `@types/api.types.ts`
2. **Use SWR for all data fetching** - never use direct fetch in components
3. **Implement proper error handling** with retry mechanisms
4. **Use optimistic updates** for better UX
5. **Configure appropriate caching** based on data characteristics
6. **Always provide loading states** to components
7. **Use proper TypeScript types** for all API responses