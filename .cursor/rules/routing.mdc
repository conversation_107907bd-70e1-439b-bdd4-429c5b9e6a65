# Expo Router Routing Rules

## File-Based Routing
- Uses Expo Router v5.1+ for file-based routing
- Route files are located in [app/](mdc:app/) directory
- File naming conventions determine route structure

## Route Structure
```
app/
├── _layout.tsx                 // Root layout
├── (app)/                      // Route group  
│   ├── _layout.tsx            // App layout
│   └── (tabs)/                // Tab navigation group
│       ├── _layout.tsx        // Tab layout
│       └── (home,search,favorites)/  // Shared layout group
│           ├── _layout.tsx    // Shared layout
│           ├── home/
│           │   └── index.tsx  // /home route
│           ├── search/
│           │   ├── index.tsx  // /search route
│           │   ├── results.tsx // /search/results
│           │   └── perfume/
│           │       └── [id].tsx // /search/perfume/[id]
│           └── favorites/
│               └── index.tsx  // /favorites route
├── sign-in.tsx                // /sign-in route
├── register.tsx               // /register route
├── onboarding.tsx             // /onboarding route
└── +not-found.tsx             // 404 page
```

## Route Groups
- Use `(groupName)` for route groups that don't affect URL
- `(app)` - Main app routes requiring authentication
- `(tabs)` - Tab navigation routes
- `(home,search,favorites)` - Shared layout for main features

## Navigation Patterns
```typescript
import { router } from "expo-router";
import { Link } from "expo-router";

// Programmatic navigation
router.push("/search/results");
router.push(`/search/perfume/${perfumeId}`);
router.back();

// Component navigation
<Link href="/profile" asChild>
  <TouchableOpacity>
    <Text>Go to Profile</Text>
  </TouchableOpacity>
</Link>
```

## Dynamic Routes
- Use `[id].tsx` for dynamic segments
- Use `[...slug].tsx` for catch-all routes
- Access parameters via `useLocalSearchParams()`

```typescript
import { useLocalSearchParams } from "expo-router";

export default function PerfumeDetail() {
  const { id } = useLocalSearchParams<{ id: string }>();
  
  return (
    // Component content
  );
}
```

## Layout Files
- `_layout.tsx` files define layout for child routes
- Use for shared navigation, headers, or providers
- Can be nested for complex layout hierarchies

## Tab Navigation
- Defined in `(tabs)/_layout.tsx`
- Uses `@react-navigation/bottom-tabs`
- Configure tab icons, labels, and behavior

## Authentication Flow
- Unauthenticated routes: `sign-in.tsx`, `register.tsx`, `onboarding.tsx`
- Authenticated routes: Everything under `(app)/`
- Handle auth state in root layout

## Route Parameters
- Query parameters: `?param=value`
- Path parameters: `[id]`
- Access via `useLocalSearchParams()` hook

## Best Practices
- Use descriptive file names for routes
- Keep route components focused and lightweight
- Implement proper error boundaries
- Handle loading states appropriately
- Use proper TypeScript types for parameters

## Error Handling
- `+not-found.tsx` for 404 errors
- Implement error boundaries in layouts
- Handle navigation errors gracefully

## Performance Considerations
- Use lazy loading for heavy screens
- Implement proper screen tracking
- Optimize route transitions
- Cache route data appropriately

## Deep Linking
- Configure deep linking in [app.json](mdc:app.json)
- Handle incoming deep links properly
- Validate deep link parameters

## Navigation State
- Use navigation state for complex flows
- Implement proper back button handling
- Handle tab state preservation
